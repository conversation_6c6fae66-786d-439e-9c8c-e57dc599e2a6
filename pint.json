{"preset": "psr12", "rules": {"array_indentation": true, "concat_space": {"spacing": "one"}, "function_declaration": {"closure_fn_spacing": "none", "closure_function_spacing": "one", "trailing_comma_single_line": false}, "method_chaining_indentation": true, "no_trailing_comma_in_singleline": {"elements": ["arguments", "array_destructuring", "array", "group_import"]}, "no_whitespace_before_comma_in_array": {"after_heredoc": false}, "ordered_imports": {"imports_order": null, "sort_algorithm": "alpha"}, "single_quote": {"strings_containing_single_quote_chars": true}, "single_space_around_construct": {"constructs_contain_a_single_space": ["yield_from"]}, "trailing_comma_in_multiline": {"after_heredoc": true, "elements": ["arguments", "arrays", "match", "parameters"]}, "trim_array_spaces": true, "whitespace_after_comma_in_array": {"ensure_single_space": true}}}