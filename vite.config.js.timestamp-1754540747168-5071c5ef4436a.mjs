// vite.config.js
import { defineConfig } from "file:///C:/Users/<USER>/Documents/z44/z44big-web/node_modules/.pnpm/vite@5.4.14_sass@1.85.0_terser@5.39.0/node_modules/vite/dist/node/index.js";
import laravel from "file:///C:/Users/<USER>/Documents/z44/z44big-web/node_modules/.pnpm/laravel-vite-plugin@1.2.0_v_17443eb63a25dc84e4e8316999e17680/node_modules/laravel-vite-plugin/dist/index.js";
import viteCompression from "file:///C:/Users/<USER>/Documents/z44/z44big-web/node_modules/.pnpm/vite-plugin-compression@0.5_3df56e5fc3cfd60e5b4de6b99cdfaf5d/node_modules/vite-plugin-compression/dist/index.mjs";
import { chunkSplitPlugin } from "file:///C:/Users/<USER>/Documents/z44/z44big-web/node_modules/.pnpm/vite-plugin-chunk-split@0.5_b186dfab2147f0324358f39a99833fd8/node_modules/vite-plugin-chunk-split/dist/index.mjs";
import { ViteMinifyPlugin } from "file:///C:/Users/<USER>/Documents/z44/z44big-web/node_modules/.pnpm/vite-plugin-minify@2.1.0_vi_843b00ebf7289c899d9657f8815b3cad/node_modules/vite-plugin-minify/dist/index.cjs";
import * as glob from "file:///C:/Users/<USER>/Documents/z44/z44big-web/node_modules/.pnpm/glob@11.0.1/node_modules/glob/dist/esm/index.js";
var jsFiles = [
  ...glob.sync("resources/js/**/*.js"),
  ...glob.sync("Modules/*/resources/**/*.js")
];
var vite_config_default = defineConfig({
  server: {
    host: "localhost",
    // Allow access from any IP address
    port: 8001
  },
  build: {
    // Enable minification
    minify: "terser",
    // Enable tree shaking
    rollupOptions: {
      output: {
        manualChunks: {
          "swiper-core": ["swiper"],
          "swiper-modules": ["swiper/modules"],
          "swiper-styles": ["swiper/css", "swiper/css/navigation", "swiper/css/pagination"],
          "toastify": ["toastify-js"]
        }
      }
    },
    // Enable chunk size warnings
    chunkSizeWarningLimit: 1e3
  },
  plugins: [
    ViteMinifyPlugin({}),
    chunkSplitPlugin({
      // Customize chunk splitting strategy
      strategy: "default",
      customSplitting: {
        // Split vendor chunks
        "vendor": [/node_modules/],
        // Split common chunks
        "common": [/src\/common/]
      }
    }),
    viteCompression({
      // Enable gzip compression
      algorithm: "gzip",
      ext: ".gz",
      threshold: 10240,
      // Only compress files larger than 10kb
      deleteOriginFile: false,
      // Keep original files
      compressionOptions: {
        level: 9
        // Maximum compression level
      }
    }),
    viteCompression({
      algorithm: "brotliCompress",
      ext: ".br",
      threshold: 10240,
      // Only compress files larger than 10kb
      deleteOriginFile: false,
      // Keep original files
      compressionOptions: {
        level: 11
        // Maximum compression level
      }
    }),
    laravel({
      input: [
        "resources/sass/app.scss",
        ...jsFiles,
        // MatchSchedule module assets
        "Modules/MatchSchedule/resources/assets/sass/app.scss"
      ],
      refresh: true
    })
  ],
  optimizeDeps: {
    // Enable dependency optimization
    include: ["swiper", "swiper/css", "swiper/css/navigation", "swiper/css/pagination", "toastify-js"],
    exclude: ["swiper/bundle"]
    // Exclude the full bundle to use modular imports
  }
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,ewogICJ2ZXJzaW9uIjogMywKICAic291cmNlcyI6IFsidml0ZS5jb25maWcuanMiXSwKICAic291cmNlc0NvbnRlbnQiOiBbImNvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9kaXJuYW1lID0gXCJDOlxcXFxVc2Vyc1xcXFxNYXR0aGV3LURFVlxcXFxEb2N1bWVudHNcXFxcWjQ0XFxcXHo0NGJpZy13ZWJcIjtjb25zdCBfX3ZpdGVfaW5qZWN0ZWRfb3JpZ2luYWxfZmlsZW5hbWUgPSBcIkM6XFxcXFVzZXJzXFxcXE1hdHRoZXctREVWXFxcXERvY3VtZW50c1xcXFxaNDRcXFxcejQ0YmlnLXdlYlxcXFx2aXRlLmNvbmZpZy5qc1wiO2NvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9pbXBvcnRfbWV0YV91cmwgPSBcImZpbGU6Ly8vQzovVXNlcnMvTWF0dGhldy1ERVYvRG9jdW1lbnRzL1o0NC96NDRiaWctd2ViL3ZpdGUuY29uZmlnLmpzXCI7aW1wb3J0IHsgZGVmaW5lQ29uZmlnIH0gZnJvbSBcInZpdGVcIjtcbmltcG9ydCBsYXJhdmVsIGZyb20gXCJsYXJhdmVsLXZpdGUtcGx1Z2luXCI7XG5pbXBvcnQgdml0ZUNvbXByZXNzaW9uIGZyb20gXCJ2aXRlLXBsdWdpbi1jb21wcmVzc2lvblwiO1xuaW1wb3J0IHsgY2h1bmtTcGxpdFBsdWdpbiB9IGZyb20gXCJ2aXRlLXBsdWdpbi1jaHVuay1zcGxpdFwiO1xuaW1wb3J0IHsgVml0ZU1pbmlmeVBsdWdpbiB9IGZyb20gXCJ2aXRlLXBsdWdpbi1taW5pZnlcIjtcbmltcG9ydCAqIGFzIGdsb2IgZnJvbSBcImdsb2JcIjtcblxuLy8gY29uc3QganNGaWxlcyA9IGdsb2Iuc3luYyhcInJlc291cmNlcy9qcy8qKi8qLmpzXCIpO1xuY29uc3QganNGaWxlcyA9IFtcbiAgICAuLi5nbG9iLnN5bmMoXCJyZXNvdXJjZXMvanMvKiovKi5qc1wiKSxcbiAgICAuLi5nbG9iLnN5bmMoXCJNb2R1bGVzLyovcmVzb3VyY2VzLyoqLyouanNcIilcbl07XG5cbmV4cG9ydCBkZWZhdWx0IGRlZmluZUNvbmZpZyh7XG4gICAgc2VydmVyOiB7XG4gICAgICAgIGhvc3Q6ICdsb2NhbGhvc3QnLCAvLyBBbGxvdyBhY2Nlc3MgZnJvbSBhbnkgSVAgYWRkcmVzc1xuICAgICAgICBwb3J0OiA4MDAxLFxuICAgIH0sXG4gICAgYnVpbGQ6IHtcbiAgICAgICAgLy8gRW5hYmxlIG1pbmlmaWNhdGlvblxuICAgICAgICBtaW5pZnk6ICd0ZXJzZXInLFxuICAgICAgICAvLyBFbmFibGUgdHJlZSBzaGFraW5nXG4gICAgICAgIHJvbGx1cE9wdGlvbnM6IHtcbiAgICAgICAgICAgIG91dHB1dDoge1xuICAgICAgICAgICAgICAgIG1hbnVhbENodW5rczoge1xuICAgICAgICAgICAgICAgICAgICAnc3dpcGVyLWNvcmUnOiBbJ3N3aXBlciddLFxuICAgICAgICAgICAgICAgICAgICAnc3dpcGVyLW1vZHVsZXMnOiBbJ3N3aXBlci9tb2R1bGVzJ10sXG4gICAgICAgICAgICAgICAgICAgICdzd2lwZXItc3R5bGVzJzogWydzd2lwZXIvY3NzJywgJ3N3aXBlci9jc3MvbmF2aWdhdGlvbicsICdzd2lwZXIvY3NzL3BhZ2luYXRpb24nXSxcbiAgICAgICAgICAgICAgICAgICAgJ3RvYXN0aWZ5JzogWyd0b2FzdGlmeS1qcyddLFxuICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICB9LFxuICAgICAgICB9LFxuICAgICAgICAvLyBFbmFibGUgY2h1bmsgc2l6ZSB3YXJuaW5nc1xuICAgICAgICBjaHVua1NpemVXYXJuaW5nTGltaXQ6IDEwMDAsXG4gICAgfSxcbiAgICBwbHVnaW5zOiBbXG4gICAgICAgIFZpdGVNaW5pZnlQbHVnaW4oe30pLFxuICAgICAgICBjaHVua1NwbGl0UGx1Z2luKHtcbiAgICAgICAgICAgIC8vIEN1c3RvbWl6ZSBjaHVuayBzcGxpdHRpbmcgc3RyYXRlZ3lcbiAgICAgICAgICAgIHN0cmF0ZWd5OiAnZGVmYXVsdCcsXG4gICAgICAgICAgICBjdXN0b21TcGxpdHRpbmc6IHtcbiAgICAgICAgICAgICAgICAvLyBTcGxpdCB2ZW5kb3IgY2h1bmtzXG4gICAgICAgICAgICAgICAgJ3ZlbmRvcic6IFsvbm9kZV9tb2R1bGVzL10sXG4gICAgICAgICAgICAgICAgLy8gU3BsaXQgY29tbW9uIGNodW5rc1xuICAgICAgICAgICAgICAgICdjb21tb24nOiBbL3NyY1xcL2NvbW1vbi9dLFxuICAgICAgICAgICAgfVxuICAgICAgICB9KSxcbiAgICAgICAgdml0ZUNvbXByZXNzaW9uKHtcbiAgICAgICAgICAgIC8vIEVuYWJsZSBnemlwIGNvbXByZXNzaW9uXG4gICAgICAgICAgICBhbGdvcml0aG06ICdnemlwJyxcbiAgICAgICAgICAgIGV4dDogJy5neicsXG4gICAgICAgICAgICB0aHJlc2hvbGQ6IDEwMjQwLCAvLyBPbmx5IGNvbXByZXNzIGZpbGVzIGxhcmdlciB0aGFuIDEwa2JcbiAgICAgICAgICAgIGRlbGV0ZU9yaWdpbkZpbGU6IGZhbHNlLCAvLyBLZWVwIG9yaWdpbmFsIGZpbGVzXG4gICAgICAgICAgICBjb21wcmVzc2lvbk9wdGlvbnM6IHtcbiAgICAgICAgICAgICAgICBsZXZlbDogOSwgLy8gTWF4aW11bSBjb21wcmVzc2lvbiBsZXZlbFxuICAgICAgICAgICAgfSxcbiAgICAgICAgfSksXG4gICAgICAgIHZpdGVDb21wcmVzc2lvbih7XG4gICAgICAgICAgICBhbGdvcml0aG06ICdicm90bGlDb21wcmVzcycsXG4gICAgICAgICAgICBleHQ6ICcuYnInLFxuICAgICAgICAgICAgdGhyZXNob2xkOiAxMDI0MCwgLy8gT25seSBjb21wcmVzcyBmaWxlcyBsYXJnZXIgdGhhbiAxMGtiXG4gICAgICAgICAgICBkZWxldGVPcmlnaW5GaWxlOiBmYWxzZSwgLy8gS2VlcCBvcmlnaW5hbCBmaWxlc1xuICAgICAgICAgICAgY29tcHJlc3Npb25PcHRpb25zOiB7XG4gICAgICAgICAgICAgICAgbGV2ZWw6IDExLCAvLyBNYXhpbXVtIGNvbXByZXNzaW9uIGxldmVsXG4gICAgICAgICAgICB9LFxuICAgICAgICB9KSxcbiAgICAgICAgbGFyYXZlbCh7XG4gICAgICAgICAgICBpbnB1dDogW1xuICAgICAgICAgICAgICAgIFwicmVzb3VyY2VzL3Nhc3MvYXBwLnNjc3NcIixcbiAgICAgICAgICAgICAgICAuLi5qc0ZpbGVzLFxuICAgICAgICAgICAgICAgIC8vIE1hdGNoU2NoZWR1bGUgbW9kdWxlIGFzc2V0c1xuICAgICAgICAgICAgICAgIFwiTW9kdWxlcy9NYXRjaFNjaGVkdWxlL3Jlc291cmNlcy9hc3NldHMvc2Fzcy9hcHAuc2Nzc1wiLFxuICAgICAgICAgICAgXSxcbiAgICAgICAgICAgIHJlZnJlc2g6IHRydWUsXG4gICAgICAgIH0pLFxuICAgIF0sXG4gICAgb3B0aW1pemVEZXBzOiB7XG4gICAgICAgIC8vIEVuYWJsZSBkZXBlbmRlbmN5IG9wdGltaXphdGlvblxuICAgICAgICBpbmNsdWRlOiBbJ3N3aXBlcicsICdzd2lwZXIvY3NzJywgJ3N3aXBlci9jc3MvbmF2aWdhdGlvbicsICdzd2lwZXIvY3NzL3BhZ2luYXRpb24nLCAndG9hc3RpZnktanMnXSxcbiAgICAgICAgZXhjbHVkZTogWydzd2lwZXIvYnVuZGxlJ10sIC8vIEV4Y2x1ZGUgdGhlIGZ1bGwgYnVuZGxlIHRvIHVzZSBtb2R1bGFyIGltcG9ydHNcbiAgICB9LFxufSk7XG4iXSwKICAibWFwcGluZ3MiOiAiO0FBQXFVLFNBQVMsb0JBQW9CO0FBQ2xXLE9BQU8sYUFBYTtBQUNwQixPQUFPLHFCQUFxQjtBQUM1QixTQUFTLHdCQUF3QjtBQUNqQyxTQUFTLHdCQUF3QjtBQUNqQyxZQUFZLFVBQVU7QUFHdEIsSUFBTSxVQUFVO0FBQUEsRUFDWixHQUFRLFVBQUssc0JBQXNCO0FBQUEsRUFDbkMsR0FBUSxVQUFLLDZCQUE2QjtBQUM5QztBQUVBLElBQU8sc0JBQVEsYUFBYTtBQUFBLEVBQ3hCLFFBQVE7QUFBQSxJQUNKLE1BQU07QUFBQTtBQUFBLElBQ04sTUFBTTtBQUFBLEVBQ1Y7QUFBQSxFQUNBLE9BQU87QUFBQTtBQUFBLElBRUgsUUFBUTtBQUFBO0FBQUEsSUFFUixlQUFlO0FBQUEsTUFDWCxRQUFRO0FBQUEsUUFDSixjQUFjO0FBQUEsVUFDVixlQUFlLENBQUMsUUFBUTtBQUFBLFVBQ3hCLGtCQUFrQixDQUFDLGdCQUFnQjtBQUFBLFVBQ25DLGlCQUFpQixDQUFDLGNBQWMseUJBQXlCLHVCQUF1QjtBQUFBLFVBQ2hGLFlBQVksQ0FBQyxhQUFhO0FBQUEsUUFDOUI7QUFBQSxNQUNKO0FBQUEsSUFDSjtBQUFBO0FBQUEsSUFFQSx1QkFBdUI7QUFBQSxFQUMzQjtBQUFBLEVBQ0EsU0FBUztBQUFBLElBQ0wsaUJBQWlCLENBQUMsQ0FBQztBQUFBLElBQ25CLGlCQUFpQjtBQUFBO0FBQUEsTUFFYixVQUFVO0FBQUEsTUFDVixpQkFBaUI7QUFBQTtBQUFBLFFBRWIsVUFBVSxDQUFDLGNBQWM7QUFBQTtBQUFBLFFBRXpCLFVBQVUsQ0FBQyxhQUFhO0FBQUEsTUFDNUI7QUFBQSxJQUNKLENBQUM7QUFBQSxJQUNELGdCQUFnQjtBQUFBO0FBQUEsTUFFWixXQUFXO0FBQUEsTUFDWCxLQUFLO0FBQUEsTUFDTCxXQUFXO0FBQUE7QUFBQSxNQUNYLGtCQUFrQjtBQUFBO0FBQUEsTUFDbEIsb0JBQW9CO0FBQUEsUUFDaEIsT0FBTztBQUFBO0FBQUEsTUFDWDtBQUFBLElBQ0osQ0FBQztBQUFBLElBQ0QsZ0JBQWdCO0FBQUEsTUFDWixXQUFXO0FBQUEsTUFDWCxLQUFLO0FBQUEsTUFDTCxXQUFXO0FBQUE7QUFBQSxNQUNYLGtCQUFrQjtBQUFBO0FBQUEsTUFDbEIsb0JBQW9CO0FBQUEsUUFDaEIsT0FBTztBQUFBO0FBQUEsTUFDWDtBQUFBLElBQ0osQ0FBQztBQUFBLElBQ0QsUUFBUTtBQUFBLE1BQ0osT0FBTztBQUFBLFFBQ0g7QUFBQSxRQUNBLEdBQUc7QUFBQTtBQUFBLFFBRUg7QUFBQSxNQUNKO0FBQUEsTUFDQSxTQUFTO0FBQUEsSUFDYixDQUFDO0FBQUEsRUFDTDtBQUFBLEVBQ0EsY0FBYztBQUFBO0FBQUEsSUFFVixTQUFTLENBQUMsVUFBVSxjQUFjLHlCQUF5Qix5QkFBeUIsYUFBYTtBQUFBLElBQ2pHLFNBQVMsQ0FBQyxlQUFlO0FBQUE7QUFBQSxFQUM3QjtBQUNKLENBQUM7IiwKICAibmFtZXMiOiBbXQp9Cg==
