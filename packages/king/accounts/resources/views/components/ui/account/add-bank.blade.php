@props([
    'isFromModal' => false,
    'withdrawData' => null
])

<form id="add_banking_submit" class="flex flex-col gap-[32px]">
    <div class="flex flex-col gap-[24px] items-start">
        @if ($isFromModal)
            <h2 class="mx-auto text-[18px] leading-[26px] font-bold text-neutral-800 uppercase">Thêm tài khoản ngân hàng</h2>
        @else
            <h2 class="text-[14px] leading-[20px] font-medium text-neutral-800">Thông tin người nhận</h2>
        @endif
        <x-kit.dropdown-new label="Ngân hàng" isRequire name="bank_code" search type="bank" :options="$withdrawData['bankList']" placeholderIcon="icon-bank" placeholder="Chọn ngân hàng của bạn"></x-kit.dropdown-new>
        @if (isset($withdrawData['userBankInfo'] -> bank_account_name))
            <x-kit.input 
                :value="$withdrawData['userBankInfo'] -> bank_account_name"
                isRequire 
                name="bank_account_name" 
                label="Chủ tài khoản" 
                placeholder="Nhập tên tài khoản"
                disabled
            >
            </x-kit.input>
        @else
            <x-kit.input 
                isRequire 
                name="bank_account_name" 
                label="Chủ tài khoản" 
                placeholder="Nhập tên tài khoản"
            >
            </x-kit.input>
        @endif
  
        <x-kit.input name="bank_account_no" isRequire type="number" label="Số tài khoản" placeholder="Nhập số tài khoản"></x-kit.input>
    </div>
    <div class="flex flex-col gap-[36px]">
        <div class="flex flex-col gap-[32px]">
            <x-kit.button disabled buttonType="submit" class="button-submit hidden mx-auto xl:flex">Thêm tài khoản</x-kit.button>

            @if (!$isFromModal)
                <div class="flex flex-col gap-[8px] text-[14px] leading-[20px] text-neutral-800">
                    <p class="font-bold">Lưu ý</p>
                    <div class="flex items-center gap-[4px]">
                        <span class="w-[4px] h-[4px] rounded-full bg-neutral-800"></span>
                        <p>
                            Chúng tôi không chịu trách nhiệm cho việc điền sai thông tin hoặc để lộ thông tin nhận tiền của quý khách
                        </p>
                    </div>
                </div>
            @else
                <p class="text-[14px] leading-[20px] text-center">Sau khi thêm tài khoản thành công, xin liên hệ với bộ phận CSKH qua Live Chat hoặc Hotline để xác minh tài khoản nhanh chóng.</p>
            @endif

        </div>
        <x-kit.button buttonType="submit" disabled class="button-submit flex w-full mx-auto xl:hidden">Thêm tài khoản</x-kit.button>
    </div>
</form>