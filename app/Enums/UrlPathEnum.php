<?php

namespace App\Enums;

enum UrlPathEnum: string
{
    case LOGIN = '/login';

    case SPORTS = '/ca-do-bong-da';

    case CASINO = '/song-bai-livecasino-truc-tuyen';

    case GAME = '/cong-game';

    case GAME_OTHER = '/cong-game/game-khac';

    case GAME_OTHER_DAGA28 = '/cong-game/game-khac?filter=&type=&keyword=&p=ga28';

    case GAME_CARD = '/cong-game/game-bai';

    case NOHU = '/cong-game/no-hu';

    case LOTTERY = '/cong-game/quay-so-number-games';

    case QUAYSO = '/quayso';

    case LODE = '/lode';
    case LODE_3M = '/lode-3-mien';

    case FISHING = '/cong-game/ban-ca';

    case FIGHTCOCK = '/daga';
    case FIGHTCOCK_WS = '/daga/ws168';

    case EVENTS = '/events';

    case EVENTS_ALL = '/events?tab=all';

    case EVENTS_PROMOTIONS = '/events?tab=promotions';

    case EVENTS_EVENTS = '/events?tab=events';

    case HOME = '/';

    case HELP = '/tro-giup';

    case KENO = '/cong-game/keno';

    case SLOTS = '/cong-game/quay-slots';

    case XOSO = '/cong-game/xo-so';

    case SABA_SPORTS = '/ca-do-bong-da/saba-sports';

    case SABA_SPORTS_VITUAL = '/ca-do-bong-da/virtual-saba-sports';

    case IM_SPORTS = '/ca-do-bong-da/virtual-im-sports';

    case K_SPORTS = '/ca-do-bong-da/ksports';

    case K_SPORTS_VITUAL = '/ca-do-bong-da/virtual-k-sports';

    case BTI_SPORTS = '/ca-do-bong-da/bti-sports';

    case IM_NORMAL_SPORTS = '/ca-do-bong-da/im-sports';

    case PP_SPORTS = '/ca-do-bong-da/virtual-pp-sports';

    case E_SPORTS = '/athena/esportsUrl';

    case NEWS = '/tin-tuc';

    case NEWS_DETAIL = '/tin-tuc/post';

    case POLICY = '/chinh-sach-bao-mat';

    case DISCLAIMER = '/mien-trach-nhiem';

    case QUESTION = '/cau-hoi-thuong-gap';

    case TERMS = '/dieu-khoan-dieu-kien';

    case INSTRUCTION_OF_REGISTER = '/huong-dan-dang-ky';

    case INSTRUCTION_OF_DEPOSIT = '/huong-dan-nap-tien';

    case INSTRUCTION_OF_WITHDRAW = '/huong-dan-rut-tien';

    case INSTRUCTION_OF_P2P_TRANSACTION = '/huong-dan-giao-dich-p2p';

    case ABOUT_US = '/ve-chung-toi';

    case ACCOUNT_HISTORY = '/account/history';

    case MATCH_SCHEDULE = '/bong-da/lich-thi-dau';
}
