<?php

use App\Helpers\ImageHelper;

if (!function_exists('optimized_image')) {
    /**
     * Generate optimized image HTML with modern formats
     * 
     * @param string $src
     * @param string $alt
     * @param string $class
     * @param string $fallback
     * @param array $attributes
     * @return string
     */
    function optimized_image($src, $alt = '', $class = '', $fallback = null, $attributes = [])
    {
        return ImageHelper::generatePictureElement($src, $alt, $class, $fallback, $attributes);
    }
}

if (!function_exists('image_url_with_cache')) {
    /**
     * Get image URL with cache busting
     * 
     * @param string $path
     * @param bool $addTimestamp
     * @return string
     */
    function image_url_with_cache($path, $addTimestamp = true)
    {
        return ImageHelper::getImageUrl($path, $addTimestamp);
    }
}

if (!function_exists('get_modern_image_sources')) {
    /**
     * Get array of modern image format sources
     * 
     * @param string $imagePath
     * @param string $fallback
     * @return array
     */
    function get_modern_image_sources($imagePath, $fallback = null)
    {
        return ImageHelper::getOptimizedImageSources($imagePath, $fallback);
    }
}

if (!function_exists('responsive_image')) {
    /**
     * Generate responsive image with srcset for different screen sizes
     * 
     * @param string $basePath
     * @param array $sizes
     * @param string $alt
     * @param string $class
     * @param string $defaultSize
     * @return string
     */
    function responsive_image($basePath, $sizes = [], $alt = '', $class = '', $defaultSize = 'md')
    {
        if (empty($sizes)) {
            $sizes = [
                'sm' => '480w',
                'md' => '768w', 
                'lg' => '1024w',
                'xl' => '1200w'
            ];
        }
        
        $pathInfo = pathinfo($basePath);
        $directory = $pathInfo['dirname'] !== '.' ? $pathInfo['dirname'] . '/' : '';
        $filename = $pathInfo['filename'];
        $extension = $pathInfo['extension'] ?? '';
        
        $srcset = [];
        $sources = [];
        
        // Generate srcset for different formats
        foreach (['avif', 'webp', $extension] as $format) {
            $formatSrcset = [];
            foreach ($sizes as $size => $width) {
                $imagePath = $directory . $filename . '-' . $size . '.' . $format;
                if (file_exists(public_path($imagePath))) {
                    $formatSrcset[] = '/' . $imagePath . ' ' . $width;
                }
            }
            
            if (!empty($formatSrcset)) {
                $sources[] = [
                    'srcset' => implode(', ', $formatSrcset),
                    'type' => ImageHelper::getMimeType($format)
                ];
            }
        }
        
        // Default image
        $defaultImage = $directory . $filename . '-' . $defaultSize . '.' . $extension;
        if (!file_exists(public_path($defaultImage))) {
            $defaultImage = $basePath;
        }
        
        $html = '<picture>';
        
        // Add source elements
        foreach (array_slice($sources, 0, -1) as $source) {
            $html .= sprintf(
                '<source srcset="%s" type="%s">',
                $source['srcset'],
                $source['type']
            );
        }
        
        // Add img element
        $html .= sprintf(
            '<img src="/%s" alt="%s" class="%s" loading="lazy" decoding="async">',
            $defaultImage,
            htmlspecialchars($alt),
            htmlspecialchars($class)
        );
        
        $html .= '</picture>';
        
        return $html;
    }
}
