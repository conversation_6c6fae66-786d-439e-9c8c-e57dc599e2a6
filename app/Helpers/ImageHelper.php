<?php

namespace App\Helpers;

class ImageHelper
{
    /**
     * Get optimized image source with modern format support
     * 
     * @param string $imagePath
     * @param string $fallback
     * @return array
     */
    public static function getOptimizedImageSources($imagePath, $fallback = null)
    {
        $basePath = public_path();
        $webPath = str_replace(public_path(), '', $imagePath);
        
        // Remove leading slash if present
        $webPath = ltrim($webPath, '/');
        
        $pathInfo = pathinfo($webPath);
        $directory = $pathInfo['dirname'] !== '.' ? $pathInfo['dirname'] . '/' : '';
        $filename = $pathInfo['filename'];
        $extension = $pathInfo['extension'] ?? '';
        
        $sources = [];
        
        // Check for AVIF format (best compression)
        $avifPath = $basePath . '/' . $directory . $filename . '.avif';
        if (file_exists($avifPath)) {
            $sources[] = [
                'srcset' => '/' . $directory . $filename . '.avif',
                'type' => 'image/avif'
            ];
        }
        
        // Check for WebP format (good compression, wide support)
        $webpPath = $basePath . '/' . $directory . $filename . '.webp';
        if (file_exists($webpPath)) {
            $sources[] = [
                'srcset' => '/' . $directory . $filename . '.webp',
                'type' => 'image/webp'
            ];
        }
        
        // Original format as fallback
        $originalPath = $basePath . '/' . $webPath;
        if (file_exists($originalPath)) {
            $sources[] = [
                'srcset' => '/' . $webPath,
                'type' => self::getMimeType($extension)
            ];
        } elseif ($fallback) {
            $sources[] = [
                'srcset' => $fallback,
                'type' => self::getMimeType(pathinfo($fallback, PATHINFO_EXTENSION))
            ];
        }
        
        return $sources;
    }
    
    /**
     * Get MIME type for image extension
     * 
     * @param string $extension
     * @return string
     */
    private static function getMimeType($extension)
    {
        $mimeTypes = [
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'png' => 'image/png',
            'gif' => 'image/gif',
            'webp' => 'image/webp',
            'avif' => 'image/avif',
            'svg' => 'image/svg+xml'
        ];
        
        return $mimeTypes[strtolower($extension)] ?? 'image/jpeg';
    }
    
    /**
     * Generate picture element HTML with modern formats
     * 
     * @param string $imagePath
     * @param string $alt
     * @param string $class
     * @param string $fallback
     * @param array $attributes
     * @return string
     */
    public static function generatePictureElement($imagePath, $alt = '', $class = '', $fallback = null, $attributes = [])
    {
        $sources = self::getOptimizedImageSources($imagePath, $fallback);
        
        if (empty($sources)) {
            return '';
        }
        
        $html = '<picture>';
        
        // Add source elements for modern formats (excluding the last one which is fallback)
        for ($i = 0; $i < count($sources) - 1; $i++) {
            $source = $sources[$i];
            $html .= sprintf(
                '<source srcset="%s" type="%s">',
                $source['srcset'],
                $source['type']
            );
        }
        
        // Add img element as fallback
        $lastSource = end($sources);
        $imgAttributes = array_merge([
            'src' => $lastSource['srcset'],
            'alt' => $alt,
            'loading' => 'lazy',
            'decoding' => 'async'
        ], $attributes);
        
        if ($class) {
            $imgAttributes['class'] = $class;
        }
        
        $html .= '<img';
        foreach ($imgAttributes as $key => $value) {
            $html .= sprintf(' %s="%s"', $key, htmlspecialchars($value));
        }
        $html .= '>';
        
        $html .= '</picture>';
        
        return $html;
    }
    
    /**
     * Get image URL with cache busting
     * 
     * @param string $imagePath
     * @param bool $addTimestamp
     * @return string
     */
    public static function getImageUrl($imagePath, $addTimestamp = false)
    {
        $url = asset($imagePath);
        
        if ($addTimestamp && file_exists(public_path($imagePath))) {
            $timestamp = filemtime(public_path($imagePath));
            $url .= '?v=' . $timestamp;
        }
        
        return $url;
    }
}
