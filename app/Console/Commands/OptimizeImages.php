<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Process;

class OptimizeImages extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'images:optimize 
                            {path? : Specific path to optimize (relative to public/asset/images)}
                            {--quality=50 : Image quality (1-100)}
                            {--formats=avif,webp : Comma-separated list of formats to generate}
                            {--force : Force re-conversion of existing files}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Optimize images by converting to modern formats (AVIF, WebP)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $path = $this->argument('path') ?? '';
        $quality = $this->option('quality');
        $formats = explode(',', $this->option('formats'));
        $force = $this->option('force');

        $inputDir = public_path('asset/images' . ($path ? '/' . $path : ''));
        
        if (!File::exists($inputDir)) {
            $this->error("Directory does not exist: {$inputDir}");
            return 1;
        }

        $this->info("Starting image optimization...");
        $this->info("Input directory: {$inputDir}");
        $this->info("Quality: {$quality}");
        $this->info("Formats: " . implode(', ', $formats));

        $processedCount = 0;
        $skippedCount = 0;
        $errorCount = 0;

        $this->processDirectory($inputDir, $formats, $quality, $force, $processedCount, $skippedCount, $errorCount);

        $this->newLine();
        $this->info("Image optimization completed!");
        $this->table(
            ['Status', 'Count'],
            [
                ['Processed', $processedCount],
                ['Skipped', $skippedCount],
                ['Errors', $errorCount],
            ]
        );

        return 0;
    }

    /**
     * Process directory recursively
     */
    private function processDirectory($dir, $formats, $quality, $force, &$processedCount, &$skippedCount, &$errorCount)
    {
        $files = File::allFiles($dir);

        foreach ($files as $file) {
            $filePath = $file->getPathname();
            $extension = strtolower($file->getExtension());

            // Skip if not an image file
            if (!in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp'])) {
                continue;
            }

            // Skip SVG files
            if ($extension === 'svg') {
                continue;
            }

            $this->processImage($filePath, $formats, $quality, $force, $processedCount, $skippedCount, $errorCount);
        }
    }

    /**
     * Process individual image
     */
    private function processImage($filePath, $formats, $quality, $force, &$processedCount, &$skippedCount, &$errorCount)
    {
        $pathInfo = pathinfo($filePath);
        $directory = $pathInfo['dirname'];
        $filename = $pathInfo['filename'];
        $extension = $pathInfo['extension'];

        $this->line("Processing: {$pathInfo['basename']}");

        foreach ($formats as $format) {
            $format = trim($format);
            $outputPath = $directory . '/' . $filename . '.' . $format;

            // Skip if file exists and not forcing
            if (File::exists($outputPath) && !$force) {
                $this->line("  Skipping {$format} (already exists)");
                $skippedCount++;
                continue;
            }

            try {
                $this->convertImage($filePath, $outputPath, $format, $quality);
                $this->line("  ✓ Created {$format} version");
                $processedCount++;
            } catch (\Exception $e) {
                $this->error("  ✗ Failed to create {$format} version: " . $e->getMessage());
                $errorCount++;
            }
        }
    }

    /**
     * Convert image using Node.js script
     */
    private function convertImage($inputPath, $outputPath, $format, $quality)
    {
        // Use the existing Node.js script
        $nodeScript = base_path('convert-and-compress-images.mjs');
        
        if (!File::exists($nodeScript)) {
            throw new \Exception("Node.js conversion script not found");
        }

        // Create a temporary script for single file conversion
        $tempScript = base_path('temp-convert-single.mjs');
        $scriptContent = $this->generateSingleFileScript($inputPath, $outputPath, $format, $quality);
        
        File::put($tempScript, $scriptContent);

        try {
            $result = Process::run("node {$tempScript}");
            
            if ($result->failed()) {
                throw new \Exception("Conversion failed: " . $result->errorOutput());
            }
        } finally {
            // Clean up temporary script
            if (File::exists($tempScript)) {
                File::delete($tempScript);
            }
        }
    }

    /**
     * Generate Node.js script for single file conversion
     */
    private function generateSingleFileScript($inputPath, $outputPath, $format, $quality)
    {
        return <<<JS
import sharp from 'sharp';
import fs from 'fs-extra';

const inputPath = '{$inputPath}';
const outputPath = '{$outputPath}';
const format = '{$format}';
const quality = {$quality};

async function convertImage() {
    try {
        if (format === 'avif') {
            await sharp(inputPath)
                .avif({ quality: quality })
                .toFile(outputPath);
        } else if (format === 'webp') {
            await sharp(inputPath)
                .webp({ quality: quality })
                .toFile(outputPath);
        } else {
            throw new Error('Unsupported format: ' + format);
        }
        console.log('Conversion successful');
    } catch (error) {
        console.error('Conversion failed:', error.message);
        process.exit(1);
    }
}

convertImage();
JS;
    }
}
