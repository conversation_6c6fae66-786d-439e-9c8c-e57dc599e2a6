<?php

namespace App\Http\Controllers\HelpController;

use App\Enums\UrlPathEnum;

use function App\Helpers\generateSeoMetaData;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class HelpController extends Controller
{
    public function index(Request $request)
    {
        //config SEO
        generateSeoMetaData('tro-giup');

        $heroBanner = config('home.heroBanner');
        $linksList = [
            ['name' => __('common.p2p_instruction'), 'url' => UrlPathEnum::INSTRUCTION_OF_P2P_TRANSACTION->value],
            ['name' => __('common.register_instruction'), 'url' => UrlPathEnum::INSTRUCTION_OF_REGISTER->value],
            ['name' => __('common.deposit_instruction'), 'url' => UrlPathEnum::INSTRUCTION_OF_DEPOSIT->value],
            ['name' => __('common.withdraw_instruction'), 'url' => UrlPathEnum::INSTRUCTION_OF_WITHDRAW->value],
            ['name' => __('common.about_us'), 'url' => UrlPathEnum::ABOUT_US->value],
            ['name' => __('common.privacy_policy'), 'url' => UrlPathEnum::POLICY->value],
            ['name' => __('common.disclaimer'), 'url' => UrlPathEnum::DISCLAIMER->value],
            ['name' => __('common.terms'), 'url' => UrlPathEnum::TERMS->value],
            ['name' => __('common.question'), 'url' => UrlPathEnum::QUESTION->value],
            ['name' => __('common.news'), 'url' => UrlPathEnum::NEWS->value],
            ['name' => __('common.match_schedule'), 'url' => UrlPathEnum::MATCH_SCHEDULE->value],
        ];
        return view('pages.help', ['heroBanner' => $heroBanner, 'linksList' => $linksList]);
    }
}
