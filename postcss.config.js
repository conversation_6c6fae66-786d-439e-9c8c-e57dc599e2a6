export default {
  plugins: {
    tailwindcss: {},
    autoprefixer: {},
    // CSS optimization for production
    ...(process.env.NODE_ENV === 'production' && {
      cssnano: {
        preset: ['default', {
          discardComments: {
            removeAll: true,
          },
          normalizeWhitespace: true,
          discardDuplicates: true,
          mergeRules: true,
          minifySelectors: true,
          reduceIdents: false, // Keep for compatibility
          zindex: false, // Keep z-index values as is
        }],
      },
    }),
  },
}
