import { defineConfig } from "vite";
import laravel from "laravel-vite-plugin";
import viteCompression from "vite-plugin-compression";
import { chunkSplitPlugin } from "vite-plugin-chunk-split";
import { ViteMinifyPlugin } from "vite-plugin-minify";
import * as glob from "glob";

// const jsFiles = glob.sync("resources/js/**/*.js");
const jsFiles = [
    ...glob.sync("resources/js/**/*.js"),
    ...glob.sync("Modules/*/resources/**/*.js")
];

export default defineConfig({
    server: {
        host: 'localhost', // Allow access from any IP address
        port: 8001,
    },
    build: {
        // Enable minification
        minify: 'terser',
        // Enable tree shaking
        rollupOptions: {
            output: {
                manualChunks: {
                    'swiper-core': ['swiper'],
                    'swiper-modules': ['swiper/modules'],
                    'swiper-styles': ['swiper/css', 'swiper/css/navigation', 'swiper/css/pagination'],
                    'toastify': ['toastify-js'],
                },
                // Optimize asset file names for better caching
                assetFileNames: (assetInfo) => {
                    const fileName = assetInfo.names?.[0] || 'asset';
                    const info = fileName.split('.');
                    const ext = info[info.length - 1];
                    if (/png|jpe?g|svg|gif|tiff|bmp|ico/i.test(ext)) {
                        return `assets/images/[name]-[hash][extname]`;
                    }
                    if (/css/i.test(ext)) {
                        return `assets/css/[name]-[hash][extname]`;
                    }
                    if (/js/i.test(ext)) {
                        return `assets/js/[name]-[hash][extname]`;
                    }
                    return `assets/[name]-[hash][extname]`;
                },
                chunkFileNames: 'assets/js/[name]-[hash].js',
                entryFileNames: 'assets/js/[name]-[hash].js',
            },
        },
        // Enable chunk size warnings
        chunkSizeWarningLimit: 1000,
        // Optimize assets
        assetsInlineLimit: 4096, // Inline assets smaller than 4kb
    },
    plugins: [
        ViteMinifyPlugin({}),
        chunkSplitPlugin({
            // Customize chunk splitting strategy
            strategy: 'default',
            customSplitting: {
                // Split vendor chunks
                'vendor': [/node_modules/],
                // Split common chunks
                'common': [/src\/common/],
            }
        }),
        viteCompression({
            // Enable gzip compression
            algorithm: 'gzip',
            ext: '.gz',
            threshold: 10240, // Only compress files larger than 10kb
            deleteOriginFile: false, // Keep original files
            compressionOptions: {
                level: 9, // Maximum compression level
            },
        }),
        viteCompression({
            algorithm: 'brotliCompress',
            ext: '.br',
            threshold: 10240, // Only compress files larger than 10kb
            deleteOriginFile: false, // Keep original files
            compressionOptions: {
                level: 11, // Maximum compression level
            },
        }),
        laravel({
            input: [
                "resources/sass/app.scss", // Critical CSS only
                "resources/sass/non-critical.scss", // Non-critical CSS
                "resources/js/css-loader.js", // CSS loading logic
                ...(process.env.NODE_ENV === 'development' ? ["resources/js/css-validator.js"] : []), // CSS validation in dev
                ...jsFiles,
                // MatchSchedule module assets
                "Modules/MatchSchedule/resources/assets/sass/app.scss",
            ],
            refresh: true,
        }),
    ],
    optimizeDeps: {
        // Enable dependency optimization
        include: ['swiper', 'swiper/css', 'swiper/css/navigation', 'swiper/css/pagination', 'toastify-js'],
        exclude: ['swiper/bundle'], // Exclude the full bundle to use modular imports
    },
});
