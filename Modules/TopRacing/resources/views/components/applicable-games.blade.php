@php
    $swiperConfig = [
        'slidesPerView' => 5.233,
        'spaceBetween' => 16,
        'navigation' => [
            'prevEl' => '.applicable-games__prev',
            'nextEl' => '.applicable-games__next',
        ],
    ];

    $brand = config('app.brand_name');
    $listGame = config('topracing.listGame');
@endphp

<div class="hidden flex-col gap-4 w-full xl:flex">
    <div class="flex justify-between items-center">
        <div class="flex items-center gap-1">
            <p class="w-[151px] text-[18px] leading-[26px] font-semibold text-black text-center uppercase">game áp dụng</p>
            <div class="flex">
                <div class="w-[2px] h-auto bg-secondary-600"></div>
                <div class="pl-2 pr-[10] py-1 bg-applicable-games-bg">
                    <p class="text-[12px] leading-[18px] text-secondary-500 italic">Áp dụng cho tất cả sản phẩm do {{ $brand }} cung cấp.</p>
                </div>
            </div>
        </div>
        <div class="flex gap-2">
            <button class="applicable-games__nav text-primary-600 w-8 h-8 text-[16px] flex justify-center items-center applicable-games__prev bg-neutral-100 rounded-full [&.swiper-button-disabled]:bg-neutral-50 [&.swiper-button-disabled>i::before]:text-neutral-300">
                <i class="icon-arrow-left-fill"></i>
            </button>
            <button class="applicable-games__nav text-primary-600 w-8 h-8 text-[16px] flex justify-center items-center applicable-games__next bg-neutral-100 rounded-full [&.swiper-button-disabled]:bg-neutral-50 [&.swiper-button-disabled>i::before]:text-neutral-300">
                <i class="icon-arrow-right-fill"></i>
            </button>
        </div>
    </div>
    <div class="w-full">
        <x-kit.swiper :swiperConfig="$swiperConfig" swiperRequiredClass="applicable-games-swiper">
            @foreach ($listGame as $game)
                <div class="swiper-slide w-full max-w-[224px] mr-4">
                    <a
                        @if ($game['type'] === 'link')
                            href="{{ $game['link'] }}"
                        @else
                            onclick="openGameMenuItem('{{ $game['link'] }}')"
                        @endif
                    >
                        <img alt="game" class="w-full aspect-[224/250] rounded-[12px] cursor-pointer" src="{{ Module::asset($game['img']) }}" loading="lazy"/>
                    </a>
                </div>
            @endforeach
        </x-kit.swiper>
    </div>
 </div>
