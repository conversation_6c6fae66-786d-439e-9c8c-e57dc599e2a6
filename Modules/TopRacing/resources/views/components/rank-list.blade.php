@props(['data'])

@php
    $showRank = false;
    $showEmpty = true;
    $typeEmpty = 'before';

    $restRankLink = $data['restRankLink'] ?? [];
    $countdown = $data['countdown'] ?? [];
    $status = $data['status'] ?? [];

    if ($status !== 'before') {
        if (count($restRankLink) === 0) {
            $typeEmpty = "empty";
        } else {
            $showRank = true;
            $showEmpty = false;
        }
    }
@endphp

<div class="flex flex-col gap-[1px] w-full xl:gap-1 xl:h-full">
    <div class="flex w-full h-9 bg-rank-list-header-table rounded-[8px] xl:h-[50px]">
        <div class="flex items-center w-[17.515%] h-full pl-[10px] xl:w-[22.667%] xl:pl-[46px]">
            <p class="text-[10px] leading-[14px] font-medium text-neutral-1000 uppercase xl:text-[16px] xl:leading-[24px]">HẠNG</p>
        </div>
        <div class="flex items-center w-[24.295%] h-full xl:w-[21.583%]">
            <p class="text-[10px] leading-[14px] font-medium text-neutral-1000 uppercase xl:text-[16px] xl:leading-[24px]">NGƯỜI CHƠI</p>
        </div>
        <div class="flex justify-end items-center w-[23.165%] h-full xl:w-[21.667%]">
            <p class="text-[10px] leading-[14px] font-medium text-neutral-1000 uppercase xl:text-[16px] xl:leading-[24px]">TỔNG CƯỢC</p>
        </div>
        <div class="flex justify-end items-center grow h-full pr-[14px] xl:pr-[63px]">
            <p class="text-[10px] leading-[14px] font-medium text-neutral-1000 uppercase xl:text-[16px] xl:leading-[24px]">TIỀN THƯỞNG</p>
        </div>
    </div>
    <div class="relative w-full h-[259px] xl:h-[calc(100%-54px)]">
        <div class="w-full h-full overflow-auto no-scrollbar">
            <x-topracing::rank-empty :show="$showEmpty" :type="$typeEmpty" :countdown="$countdown"/>
            <div class="rank-list-container flex-col gap-[1px] w-full h-max xl:gap-1 {{ $showRank ? "flex" : "hidden" }}">
                @foreach ($restRankLink as $key => $item)
                    @php
                        $index = $key + 4;
                        $itemData = $item;
                        $activeClass = '';

                        if ($data['rankPosition'] && $data['rankPosition'] -> index === $index) {
                            $activeClass = 'active';
                            $itemData = $data['rankPosition'];
                        }
                    @endphp

                    <x-topracing::rank-item :activeClass="$activeClass" :data="$itemData" :key="$index" :username="$itemData -> username" :amount="$itemData -> amount" :turnover="$itemData -> turnover"/>
                @endforeach
            </div>
        </div>
        <x-topracing::rank-loading/>
    </div>
</div>

<script>
    window.createRankItem = (data, key, activeClass) => `<x-topracing::rank-item activeClass="${activeClass}" key="${key}" username="${data.username}" amount="${formatMoney(data.amount)}" turnover="${formatMoney(data.turnover)}"/>`;
</script>
