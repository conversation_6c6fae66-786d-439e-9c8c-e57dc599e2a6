<?php

namespace Modules\RewardGoldenHour\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Modules\RewardGoldenHour\Services\RewardGoldenHourService;
use function App\Helpers\generateSeoMetaData;


class RewardGoldenHourController extends Controller
{
    protected $rewardGoldenHourService;

    public function __construct(RewardGoldenHourService $rewardGoldenHourService)
    {
        $this->rewardGoldenHourService = $rewardGoldenHourService;
    }
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $userTurnover = null;
        $rewardList = $this->rewardGoldenHourService->getRewardList();
        if (Auth::check()) {
            $userTurnover = $this->rewardGoldenHourService->getUserTurnover();
        }
        generateSeoMetaData('san-thuong-gio-vang');
        return view('rewardgoldenhour::index', compact('rewardList', 'userTurnover'));
    }
    
}
