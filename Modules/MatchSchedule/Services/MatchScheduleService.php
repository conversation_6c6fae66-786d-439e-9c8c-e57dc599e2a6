<?php

namespace Modules\MatchSchedule\Services;

use Illuminate\Support\Facades\Blade;
use Nwidart\Modules\Traits\PathNamespace;
use RecursiveDirectoryIterator;
use RecursiveIteratorIterator;
use App\Services\GatewayApi;
use Illuminate\Support\Facades\App;
use App\Services\FakerApi;
class MatchScheduleService
{
    private ?GatewayApi $gatewayApi;
    private ?FakerApi $fakerApi;

    public function __construct(GatewayApi $gatewayApi, FakerApi $fakerApi)
    {
        $this->gatewayApi = $gatewayApi;
        $this->fakerApi = $fakerApi;
    }

    public function __destruct()
    {
        $this->gatewayApi = null;
    }

    public function getMatchSchedule($params = [])
    {
        $endpoint = config('matchschedule.endpoint.seasons_summaries');


        try {
            $response = $this->gatewayApi->getPromotion(
                endpoint: $endpoint,
                queryparams: $params,
            );

            if (isset($response->status) && $response->status === 'OK') {
                return $response->data;
            }

            return [];
        } catch (\Exception $e) {
            return [
                'status' => 'BAD_REQUEST',
                'message' => $e->getMessage()
            ];
        }
    }

    public function getLeagues()
    {
        $endpoint = config('matchschedule.endpoint.competitions');
        $limit = config('matchschedule.competitions_limit');
        $cacheKey = 'match_schedule_leagues';
        $cacheTime = 60 * 60 * 1; // 1 hour

        try {
            return cache()->remember($cacheKey, $cacheTime, function () use ($endpoint, $limit) {
                $response = $this->gatewayApi->getPromotion(
                    endpoint: $endpoint, 
                    queryparams: [
                        'limit' => $limit,
                    ]
                );

                if (isset($response->status) && $response->status === 'OK') {
                    return $response->data;
                }

                return [];
            });
        } catch (\Exception $e) {
            return [
                'status' => 'BAD_REQUEST',
                'message' => $e->getMessage()
            ];
        }
    }

    public function getNewsByCategory($params = [])
    {
        $endpoint = config('matchschedule.endpoint.news_by_category');
        $params = array_merge($params, ['limit' => 4, 'alias' => 'the-thao']);
        
        try {
            $response = $this->gatewayApi->get(
                endpoint: $endpoint,
                queryparams: $params,
            );
    
            if (isset($response->status) && $response->status === 'OK') {
                return $response->data;
            }
    
            return [];
        } catch (\Exception $e) {
            return [
                'status' => 'BAD_REQUEST',
                'message' => $e->getMessage()
            ];
        }
    }

    public function getRankings($params = [])
    {
        $endpoint = config('matchschedule.endpoint.standing');
        
        try {
            $response = $this->gatewayApi->getPromotion(
                endpoint: $endpoint,
                queryparams: $params,
            );
    
            if (isset($response->status) && $response->status === 'OK') {
                return $response->data;
            }
    
            return [];
        } catch (\Exception $e) {
            return [
                'status' => 'BAD_REQUEST',
                'message' => $e->getMessage()
            ];
        }
    }
}
