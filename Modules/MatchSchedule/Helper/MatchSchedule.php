<?php

namespace Modules\MatchSchedule\Helper;

use Carbon\Carbon;
use Illuminate\Support\Facades\App;

class MatchSchedule
{
    const DEFAULT_BETTING_RATE = '-';

    public static function getHandicap($item, $index, $homeKey) {
        $handicap = $item->odds->m[$index]->o[0] ?? null;
        if ($handicap) {
            return [
                'odds' => $handicap->$homeKey->ma ?? self::DEFAULT_BETTING_RATE,
                'rate' => $homeKey === 'oh' 
                    ? ($handicap->p ?? 0)
                    : ((-floatval($handicap->p)) ?: self::DEFAULT_BETTING_RATE)
            ];
        }
        return [
            'odds' => self::DEFAULT_BETTING_RATE,
            'rate' => self::DEFAULT_BETTING_RATE
        ];
    }

    public static function getFullMatch($item, $index = 2) {
        $handicap = $item->odds->m[$index]->o[0] ?? null;
        if ($handicap) {
            return [
                'oh' => $handicap->oh->de ?? self::DEFAULT_BETTING_RATE,
                'oa' => $handicap->oa->de ?? self::DEFAULT_BETTING_RATE,
                'od' => $handicap->od->de ?? self::DEFAULT_BETTING_RATE
            ];
        }
        return [
            'oh' => self::DEFAULT_BETTING_RATE,
            'oa' => self::DEFAULT_BETTING_RATE,
            'od' => self::DEFAULT_BETTING_RATE
        ];
    }

    public static function getCard($item, $index) {
        $info = $item->statistics->totals->competitors[$index] ?? null;
        if (!$info) {
            return null;
        }
        return [
            'yellow_cards' => intval($info->statistics->yellow_cards ?? 0),
            'red_cards' => intval($info->statistics->red_cards ?? 0),
            'corner_kicks' => intval($info->statistics->corner_kicks ?? 0)
        ];
    }

    public static function handicapHome($item) {
        return self::getHandicap($item, 0, 'oh');
    }

    public static function handicapAway($item) {
        return self::getHandicap($item, 0, 'oa');
    }

    public static function overRate($item) {
        return self::getHandicap($item, 1, 'oh');
    }

    public static function underRate($item) {
        return self::getHandicap($item, 1, 'oa');
    }

    public static function fullMatch($item) {
        return self::getFullMatch($item, 2);
    }

    public static function checkNumber($number) {
        if ($number > 0) {
            return 'status--up';
        }
        if ($number < 0) {
            return 'status--down';
        }
        return '';
    }

    public static function formatDateUTC($value) {
        return Carbon::parse($value)->format('d/m');
    }

    public static function formatTimeUTC($value) {
        return Carbon::parse($value)->format('H:i');
    }

    public static function formatDateUTCOdds($value) {
        return Carbon::parse($value)->format('d/m - H:i');
    }

    // Rankings outcome
    public static function convertOutcome($outcome, $isNote = false) {
        switch ($outcome) {
            case 'Champions League':
            case 'Qualified':
            case 'Champions League league stage':
            case 'UEFA CL group stage':
            case 'Semifinal':
            case 'Title Play-offs':
            case 'LIBC CL group stage':
            case 'Promotion':
            case 'Upgrade Team':
            case 'AFC Champions League Elite Group Stage':
            case 'AFC Champions League Elite League Stage':
            case 'Knockout Phase':
                return '#4286F5';
            case 'Champions League Qualification':
            case 'Qualification Playoffs':
            case 'Playoffs':
            case 'UEFA ECL Playoffs':
            case 'UEFA ECL play-offs':
            case 'Promotion Playoff':
            case 'Promotion Playoffs':
            case 'Upgrade Play-off':
            case 'Upgrade Play-offs':
            case 'UEFA ECL qualifying playoffs':
            case 'Playoff playoffs':
            case 'Playoffs: playoffs':
            case 'UEFA ECL Qualification':
            case 'UEFA ECL offs':
            case 'AFC Champions League Elite Playoff':
                return '#F97A16';
            case 'UEFA Europa League':
            case 'Europa League league stage':
            case 'Play Offs: Quarter-finals':
            case 'UEFA EL Qualification':
            case 'UEFA EL play-offs':
            case 'AFC Champions League 2 Group Stage':
                return '#0DAB1D';
            case 'Conference League Qualification':
            case 'UEFA qualifying':
            case 'UEFA  qualifying':
            case 'UEFA CL play-offs':
                return '#24C1E0';
            case 'Relegation Playoffs':
            case 'Relegation Play-offs':
                return '#FFC659';
            case 'Relegation':
            case 'Degrade Team':
            case 'Degrade  Team':
                return '#D01C2D';
            default:
                return $isNote ? '#0DAB1D' : $outcome;
        }
    }
    
    public static function convertNameLeague($name) {
        switch ($name) {
            case 'Playoffs':
                return 'Vòng đấu loại trực tiếp';
            case 'Qualified':
                return 'Vòng tiếp theo';
            case 'Europa League league stage':
                return 'Vòng bảng Europa League';
            case 'Champions League league stage':
                return 'Vòng bảng Champions League';
            case 'UEFA ECL Playoffs':
                return 'Vòng loại trực tiếp UEFA ECL';
            case 'Champions League':
                return 'Vòng bảng Vô địch các CLB Châu Âu';
            case 'Champions League Qualification':
                return 'Vòng loại Vô địch các CLB Châu Âu';
            case 'UEFA Europa League':
                return 'Vòng bảng UEFA Europa';
            case 'Conference League Qualification':
                return 'Vòng loại UEFA Europa Conference';
            case 'Relegation':
                return 'Xuống hạng';
            case 'Relegation Playoffs':
                return 'Trận quyết định đội xuống hạng';
            case 'Promotion':
                return 'Thăng hạng';
            case 'Promotion Playoffs':
                return 'Trận quyết định đội thăng hạng';
            default:
                return $name;
        }
    }
    
    public static function completedOutCome($groups) {
        $result = [];
        foreach ($groups as $group) {
            if (isset($group->current_outcome) && count($group->current_outcome) > 0) {
                $result = array_merge($result, $group->current_outcome);
            }
        }
        return array_unique($result);
    }

    public static $matchStatusMap = [
        "not_started" => [
            'label' => 'Chưa diễn ra',
            'color' => '#16A34A',
            'border' => '#BBF7D0'
        ],
        "ended" => [
            'label' => 'Kết thúc',
            'color' => '#575D6A',
            'border' => '#CFD1D9'
        ],
        "live" => [
            'label' => 'Đang diễn ra',
            'color' => '#1BA6AC',
            'border' => '#BBE4E6'
        ],
        "cancelled" => [
            'label' => 'Huỷ',
            'color' => '#DC2626',
            'border' => '#FECACA'
        ],
        "postponed" => [
            'label' => 'Tạm hoãn',
            'color' => '#CA8A04',
            'border' => '#FEF08A'
        ],
    ];

    public static function getMatchStatus($status) {
        return self::$matchStatusMap[$status] ?? self::$matchStatusMap['not_started'];
    }
}
