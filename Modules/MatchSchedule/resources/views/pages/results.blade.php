@php
    use Modules\MatchSchedule\Helper\MatchSchedule;
@endphp

<x-match-schedule::layouts.master :leagues="$leagues" :news="$news" header="Kết quả">
    <div class="js-results-list-container">
        @if (count($results) > 0)
            @foreach ($results as $index => $season)
                <div class="space-y-1">
                    <div data-season-id="{{ $season->season_info->id }}"
                        class="js-toggle-season-btn flex items-center justify-between bg-match-secondary-surface px-4 h-10 border-b {{$index == 0 ? 'border-t' : ''}} border-neutral-150 cursor-pointer">
                        <div class="flex items-center gap-2">
                            <img src="{{ $season->season_info->logo }}" alt="icon-season"
                                class="w-[24px] aspect-square">
                            <div class="text-sm text-match-primary-content font-medium">{{ $season->season_info->name }}
                            </div>
                        </div>
                        <div class="flex items-center gap-2.5">
                            <div
                                class="hidden size-6 text-sm font-medium text-match-secondary items-center justify-center js-toggle-season-count rounded bg-match-primary">
                                0
                            </div>
                            <img src="{{  asset('modules/matchschedule/images/icons/arrow-up.svg') }}"
                                alt="icon-arrow-right" class="js-toggle-season-icon w-[24px] h-[24px] z-[1]" />
                        </div>
                    </div>

                    <div data-season-id="{{ $season->season_info->id }}" class="js-season-content active">
                        @foreach ($season->events as $match)
                            <div @class([
                                'js-match-item gap-2 px-4 py-4 max-xl:justify-between max-xl:pr-[10px]',
                                'border-b border-neutral-650' => !$loop->last,
                            ])>

                                <div class="flex items-center gap-4 h-[44px]">
                                    <div class="flex text-sm items-center">
                                        <div class="text-match-secondary">
                                            {{ format_date($match->sport_event->start_time, 'd/m', 'Asia/Ho_Chi_Minh') }}
                                        </div>

                                        <span class="mx-1 text-match-secondary"> - </span>
                                        <div class="font-medium text-match-primary-content">
                                            {{ format_date($match->sport_event->start_time, 'H:i', 'Asia/Ho_Chi_Minh') }}
                                        </div>
                                    </div>
                                    <div class="flex items-center justify-center text-xs font-medium leading-4 px-2 py-[2px] max-xl:py-[1px] rounded-full"
                                        style="border: 1px solid {{ MatchSchedule::getMatchStatus($match->sport_event_status->status)['border'] }}; color: {{ MatchSchedule::getMatchStatus($match->sport_event_status->status)['color'] }};">
                                        {{ MatchSchedule::getMatchStatus($match->sport_event_status->status)['label'] }}
                                    </div>
                                </div>
                                <div class="flex items-center gap-2">

                                    <div class="flex flex-col gap-2">
                                        <div class="flex items-center gap-[10px] xl:gap-[20px] justify-between">
                                            <div class="flex items-center gap-2 xl:w-[300px] overflow-hidden">
                                                <img src="{{ $match->sport_event->competitors[0]->logo }}"
                                                    alt="icon-competitor"
                                                    class="min-w-[20px] min-h-[20px] max-w-[20px] max-h-[20px]"
                                                    onerror="this.onerror=null; this.src='{{  asset('modules/matchschedule/images/tournaments/default.avif') }}';">
                                                <div class="!text-sm text-match-primary-content flex-1">
                                                    <div class="truncate line-clamp-1 w-[108px] xl:w-full max-xl:text-xs xl:w-full xl:max-w-[240px]"
                                                        title="{{ $match->sport_event->competitors[0]->name }}">
                                                        {{ $match->sport_event->competitors[0]->name }}
                                                    </div>

                                                </div>

                                            </div>

                                            <div class="flex items-center gap-1.5 mt-[2px]">
                                                <div class="text-xs xl:w-[100px] max-xl:text-center max-[389px]:w-[60px] max-xl:w-[90px] text-match-primary-content font-medium">
                                                    {{ $match->sport_event_status->home_score ?? 0 }}
                                                </div>
                                                <div
                                                    class="flex items-center text-match-primary-content justify-between px-3 w-[88px] xl:w-[100px]">
                                                    <div class="flex items-center gap-1.5">
                                                        <div
                                                            class="w-3 h-4 bg-match-alert-status-warning rounded-[2px]">
                                                        </div>
                                                        <div class="text-xs/[18px]">
                                                            {{ $match->statistics->totals->competitors[0]->statistics->yellow_cards ?? 0 }}
                                                        </div>

                                                    </div>
                                                    <div class="flex items-center gap-1.5">
                                                        <div class="w-3 h-4 bg-match-alert-status-error rounded-[2px]">
                                                        </div>
                                                        <div class="text-xs/[18px]">
                                                            {{ $match->statistics->totals->competitors[0]->statistics->red_cards ?? 0 }}
                                                        </div>

                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="flex items-center gap-[10px] xl:gap-[20px] justify-between">
                                            <div class="flex items-center gap-2 xl:w-[300px] overflow-hidden">
                                                <img src="{{ $match->sport_event->competitors[1]->logo }}"
                                                    alt="icon-competitor"
                                                    class="min-w-[20px] min-h-[20px] max-w-[20px] max-h-[20px]"
                                                    onerror="this.onerror=null; this.src='{{  asset('modules/matchschedule/images/tournaments/default.avif') }}';">
                                                <div class="text-sm text-match-primary-content  flex-1">
                                                    <div class="truncate line-clamp-1 w-[108px] max-xl:text-xs xl:w-full xl:max-w-[240px]"
                                                        title="{{ $match->sport_event->competitors[1]->name }}">
                                                        {{ $match->sport_event->competitors[1]->name }}
                                                    </div>

                                                </div>

                                            </div>
                                            <div class="flex items-center gap-1.5 mt-[2px]">
                                                <div class="text-xs xl:w-[100px] max-[389px]:w-[60px] max-xl:w-[90px] max-xl:text-center text-match-primary-content font-medium">
                                                    {{ $match->sport_event_status->away_score ?? 0 }}
                                                </div>
                                                <div
                                                    class="flex items-center text-match-primary-content justify-between px-3 w-[88px] xl:w-[100px]">
                                                    <div class="flex items-center gap-1.5">
                                                        <div
                                                            class="w-3 h-4 bg-match-alert-status-warning rounded-[2px]">
                                                        </div>
                                                        <div class="text-xs/[18px]">
                                                            {{ $match->statistics->totals->competitors[1]->statistics->yellow_cards ?? 0 }}
                                                        </div>

                                                    </div>
                                                    <div class="flex items-center gap-1.5">
                                                        <div class="w-3 h-4 bg-match-alert-status-error rounded-[2px]">
                                                        </div>
                                                        <div class="text-xs/[18px]">
                                                            {{ $match->statistics->totals->competitors[1]->statistics->red_cards ?? 0 }}
                                                        </div>

                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        @endforeach
                    </div>
                </div>
            @endforeach
        @else
            <x-match-schedule::empty-data />
        @endif
    </div>
</x-match-schedule::layouts.master>
