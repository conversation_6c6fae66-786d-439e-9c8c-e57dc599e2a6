@php
    use Modules\MatchSchedule\Helper\MatchSchedule;

    $isSingleSeason = isset($rankings) && is_object($rankings);
    $isMultipleSeasons = isset($rankings) && is_array($rankings) && count($rankings) > 0;
@endphp

<x-match-schedule::layouts.master :leagues="$leagues" :news="$news" header="Bảng xếp hạng" :isShowDateFilter="false">
    <div class="js-rankings-list-container mt-4">
        {{-- Multiple seasons --}}
        @if ($isMultipleSeasons)
            @foreach ($rankings as $ranking)
                <div data-season-id="{{ $ranking->season->id }}"
                    class="js-toggle-season-btn flex items-center justify-between bg-match-desktop px-4 h-10 xl:p-2 border-b border-neutral-150 cursor-pointer">
                    <div class="flex items-center gap-2">
                        <img src="{{ $ranking->season->logo }}" alt="icon-season"
                            class="w-[24px] aspect-square">
                        <div class="text-sm text-match-primary-content font-medium">{{ $ranking->season->name }}</div>
                    </div>
                    <div class="w-[24px] h-[24px] ">

                        <img src="{{  asset('modules/matchschedule/images/icons/arrow-up.svg') }}" alt="icon-arrow-right"
                            class="js-toggle-season-icon w-[24px] h-[24px] z-[1]" />
                    </div>
                </div>

                <div data-season-id="{{ $ranking->season->id }}" class="js-season-content active mb-1">
                    @if (isset($ranking->standings->groups) && count($ranking->standings->groups) > 0)
                        @foreach ($ranking->standings->groups as $group)
                            <div class="">
                                @if (isset($group->id) && $group->id !== 0)
                                    <div
                                        class="flex items-center h-[40px] text-sm text-match-primary-content font-medium leading-5 max-xl:bg-neutral-790 max-xl:pl-3">
                                        Bảng {{ $group->group_name }} 
                                    </div>
                                @endif

                                @if (isset($group->standings) && count($group->standings) > 0)
                                    <div
                                        class="flex items-center justify-between px-4 max-xl:px-1.5 h-10 xl:h-8 bg-match-secondary-surface max-xl:text-xs text-sm text-match-secondary">
                                        <div class="flex items-center">
                                            <div class="w-[26px] text-center">TT</div>
                                            <div class="w-[76px] xl:w-[120px] ml-[10px] xl:ml-[32px] text-center">Đội
                                            </div>
                                        </div>
                                        <div class="flex items-center justify-between xl:gap-1">
                                            <div class="w-[29px] xl:w-[42.5px] text-center">Trận</div>
                                            <div class="w-[29px] xl:w-[42.5px] text-center">
                                                <span class="xl:hidden">Th</span>
                                                <span class="max-xl:hidden">Thắng</span>
                                            </div>
                                            <div class="w-[29px] xl:w-[42.5px] text-center">
                                                <span class="xl:hidden">H</span>
                                                <span class="max-xl:hidden">Hoà</span>
                                            </div>
                                            <div class="w-[28px] xl:w-[42.5px] text-center">
                                                <span class="xl:hidden">B</span>
                                                <span class="max-xl:hidden">Thua</span>
                                            </div>
                                            <div class="w-[33px] xl:w-[42.5px] text-center">BT</div>
                                            <div class="w-[29px] xl:w-[42.5px] text-center">SBT</div>
                                            <div class="w-[29px] xl:w-[42.5px] text-center">+/-</div>
                                            <div class="w-[30px] xl:w-[42.5px] text-center">Điểm</div>
                                        </div>
                                    </div>

                                    @foreach ($group->standings as $index => $standing)
                                        <div class="flex w-full">

                                            <div
                                                class="flex flex-1 items-center justify-between px-4 max-xl:px-1.5 max-xl:h-[39px] h-12 text-match-primary-content text-sm border-b border-neutral-150">
                                                <div class="flex items-center">
                                                    <div class="w-[26px] text-center relative text-sm">
                                                        {{ $index + 1 }}
                                                        <div class="w-6 h-1.5 max-xl:h-[3px] max-xl:w-[16px] max-xl:bottom-[-10px] absolute bottom-[-16px] rounded-t-full max-xl:left-[5px] left-0"
                                                            @if (isset($standing->current_outcome)) style="background-color: {{ MatchSchedule::convertOutcome($standing->current_outcome) }};" @endif>
                                                        </div>
                                                    </div>
                                                    <div
                                                        class="flex items-center gap-1 w-[76px] xl:w-[140px] ml-[10px] xl:ml-[32px]">
                                                        <img src="{{ $standing->competitor->logo }}"
                                                            alt="logo-competitor"
                                                            class="w-[20px] h-[20px] bg-neutral rounded-[2px]">
                                                        <div class="truncate line-clamp-1 max-xl:text-xs"
                                                            title="{{ $standing->competitor->name }}">
                                                            {{ $standing->competitor->name }}
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="flex items-center justify-between xl:gap-1 max-xl:text-xs">
                                                    <div class="w-[29px] xl:w-[42.5px] text-center">
                                                        {{ $standing->played ?? '' }}
                                                    </div>
                                                    <div class="w-[29px] xl:w-[42.5px] text-center">
                                                        {{ $standing->win ?? '' }}
                                                    </div>
                                                    <div class="w-[29px] xl:w-[42.5px] text-center">
                                                        {{ $standing->draw ?? '' }}
                                                    </div>
                                                    <div class="w-[28px] xl:w-[42.5px] text-center">
                                                        {{ $standing->loss ?? '' }}
                                                    </div>
                                                    <div class="w-[33px] xl:w-[42.5px] text-center">
                                                        {{ $standing->goals_for ?? '' }}
                                                    </div>
                                                    <div class="w-[29px] xl:w-[42.5px] text-center">
                                                        {{ $standing->goals_against ?? '' }}
                                                    </div>
                                                    <div class="w-[29px] xl:w-[42.5px] text-center">
                                                        {{ $standing->goals_diff ?? '' }}
                                                    </div>
                                                    <div class="w-[30px] xl:w-[42.5px] text-center">
                                                        {{ $standing->points ?? '' }}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                @endif
                            </div>
                        @endforeach

                        @php
                            $outcomes = MatchSchedule::completedOutCome($ranking->standings->groups);
                        @endphp

                        @if (isset($outcomes) && count($outcomes) > 0)
                            <div
                                class="flex flex-col gap-2 px-3 pt-4 xl:p-4 pb-6 max-xl:bg-neutral-790 text-match-primary-content text-sm">
                                <div class="font-medium">Ghi chú</div>

                                @foreach ($outcomes as $outcome)
                                    <div class="flex items-center gap-2 ">
                                        <div class="size-4 rounded"
                                            style="background-color: {{ MatchSchedule::convertOutcome($outcome, true) }};">
                                        </div>
                                        <div class="leading-4">{{ MatchSchedule::convertNameLeague($outcome) }}
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @endif
                    @endif
                </div>
            @endforeach
        @elseif ($isSingleSeason)
            {{-- Single season --}}
            <div data-season-id="{{ $rankings->season->id }}"
                class="js-toggle-season-btn flex items-center justify-between bg-match-desktop px-4 h-10 xl:p-2 border-b border-neutral-150 cursor-pointer">
                <div class="flex items-center gap-2">
                    <img src="{{ $rankings->season->logo }}" alt="icon-season"
                        class="w-[24px] aspect-square">
                    <div class="text-sm text-match-primary-content font-medium">{{ $rankings->season->name }}</div>
                </div>
            </div>

            <div data-season-id="{{ $rankings->season->id }}" class="js-season-content active">
                @if (isset($rankings->groups) && count($rankings->groups) > 0)
                    @foreach ($rankings->groups as $group)
                        <div class="">
                            @if (isset($group->id) && $group->id !== 0)
                                <div
                                    class="flex items-center h-9 text-sm font-medium border-b px-4 border-neutral-150 text-match-primary-content bg-match-desktop max-xl:pl-3">
                                    Bảng {{ $group->group_name }} 
                                </div>
                            @endif

                            @if (isset($group->standings) && count($group->standings) > 0)
                                <div
                                    class="flex items-center justify-between px-4 xl:pr-5 bg-match-desktop h-8 text-xs text-match-secondary">
                                    <div class="flex items-center">
                                        <div class="w-[26px] text-center">TT</div>
                                        <div class="w-[76px] xl:w-[120px] ml-[10px] xl:ml-[32px] text-center">Đội</div>
                                    </div>
                                    <div class="flex items-center justify-between xl:gap-2">
                                        <div class="w-[29px] xl:w-[36px] text-center">Trận</div>
                                        <div class="w-[29px] xl:w-[36px] text-center">
                                            <span class="xl:hidden">Th</span>
                                            <span class="max-xl:hidden">Thắng</span>
                                        </div>
                                        <div class="w-[29px] xl:w-[42.5px] text-center">
                                            <span class="xl:hidden">H</span>
                                            <span class="max-xl:hidden">Hoà</span>
                                        </div>
                                        <div class="w-[28px] xl:w-[42.5px] text-center">Thua</div>
                                        <div class="w-[33px] xl:w-[42.5px] text-center">BT</div>
                                        <div class="w-[29px] xl:w-[42.5px] text-center">SBT</div>
                                        <div class="w-[29px] xl:w-[42.5px] text-center">+/-</div>
                                        <div class="w-[30px] xl:w-[42.5px] text-center">Điểm</div>
                                    </div>
                                </div>

                                @foreach ($group->standings as $index => $standing)
                                    <div class="flex w-full">
                                        <div
                                            class="flex flex-1 items-center justify-between px-4 h-12 text-sm text-match-primary-content leading-4 border-b border-neutral-150">
                                            <div class="flex items-center">
                                                <div class="w-[26px] text-center relative">{{ $index + 1 }}

                                                    <div class="w-6 h-1.5 absolute bottom-[-16px] rounded-t left-0"
                                                        @if (isset($standing->current_outcome)) style="background-color: {{ MatchSchedule::convertOutcome($standing->current_outcome) }};" @endif>
                                                    </div>
                                                </div>
                                                <div
                                                    class="flex items-center gap-1 w-[76px] xl:w-[140px] ml-[10px] xl:ml-[32px]">
                                                    <img src="{{ $standing->competitor->logo }}" alt="logo-competitor"
                                                        class="w-[20px] h-[20px] bg-neutral rounded-[2px]">
                                                    <div class="truncate line-clamp-1"
                                                        title="{{ $standing->competitor->name }}">
                                                        {{ $standing->competitor->name }}
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="flex items-center justify-between xl:gap-2">
                                                <div class="w-[29px] xl:w-[42.5px] text-center">
                                                    {{ $standing->played ?? '' }}
                                                </div>
                                                <div class="w-[29px] xl:w-[42.5px] text-center">
                                                    {{ $standing->win ?? '' }}
                                                </div>
                                                <div class="w-[29px] xl:w-[42.5px] text-center">
                                                    {{ $standing->draw ?? '' }}
                                                </div>
                                                <div class="w-[28px] xl:w-[42.5px] text-center">
                                                    {{ $standing->loss ?? '' }}
                                                </div>
                                                <div class="w-[33px] xl:w-[42.5px] text-center">
                                                    {{ $standing->goals_for ?? '' }}
                                                </div>
                                                <div class="w-[29px] xl:w-[42.5px] text-center">
                                                    {{ $standing->goals_against ?? '' }}
                                                </div>
                                                <div class="w-[29px] xl:w-[42.5px] text-center">
                                                    {{ $standing->goals_diff ?? '' }}
                                                </div>
                                                <div class="w-[30px] xl:w-[42.5px] text-center">
                                                    {{ $standing->points ?? '' }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            @endif
                        </div>
                    @endforeach

                    @php
                        $outcomes = MatchSchedule::completedOutCome($rankings->groups);
                    @endphp

                    @if (isset($outcomes) && count($outcomes) > 0)
                        <div class="flex flex-col gap-[6px] px-3 pt-4 xl:p-4 pb-6 text-sm text-match-primary-content">
                            <div class="font-medium">Ghi chú</div>

                            @foreach ($outcomes as $outcome)
                                <div class="flex items-center gap-2">
                                    <div class="size-4 rounded"
                                        style="background-color: {{ MatchSchedule::convertOutcome($outcome, true) }};">
                                    </div>
                                    <div class="">{{ MatchSchedule::convertNameLeague($outcome) }}
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @endif
                @endif
            </div>
        @else
            <x-match-schedule::empty-data />
        @endif
    </div>
</x-match-schedule::layouts.master>
