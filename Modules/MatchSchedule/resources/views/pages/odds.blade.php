@php
    use Mo<PERSON>les\MatchSchedule\Helper\MatchSchedule;

    // Helper function để hiển thị competitor
    function renderCompetitor($competitor)
    {
        return '<div class="flex items-center gap-2">
            <img src="' .
            $competitor->logo .
            '"
                alt="icon-competitor"
                class="min-w-[20px] min-h-[20px] max-w-[20px] max-h-[20px] text-match-primary-content"
                onerror="this.onerror=null; this.src=\'' .
            asset('/modules/matchschedule/images/tournaments/default.avif') .
            '\';">
            <div class="!text-sm max-xl:!text-xs !leading-[18px] text-match-primary-content max-xl:w-[108px] w-fit flex-1 xl:w-[193px] truncate line-clamp-1"
                title="' .
            $competitor->name .
            '">
                ' .
            $competitor->name .
            '
            </div>
        </div>';
    }

    // Helper function để hiển thị odds box
    function renderOddsBox($rate, $odds, $bgClass = 'bg-neutral-100')
    {
        return '<div class="flex justify-between w-[56px] xl:w-[114px] items-center h-[32px] max-xl:text-[10px]/[14px] xl:h-[28px] px-3 max-xl:py-0.5 py-1 text-center ' .
            $bgClass .
            ' rounded-lg max-xl:flex-col">
            <div class="text-match-secondary max-xl:text-[10px]/[14px]">' .
            $rate .
            '</div>
            <div class="text-match-primary-content text-xs font-medium max-xl:text-[10px]/[14px]">' .
            $odds .
            '</div>
        </div>';
    }

    // Helper function để hiển thị header odds
    function renderOddsHeader()
    {
        return '<div class="flex items-center gap-2 text-xs xl:text-sm text-match-secondary">
            <p class="min-w-[56px] xl:min-w-[114px] text-center">Kèo chấp</p>
            <p class="min-w-[56px] xl:min-w-[114px] text-center">Tài xỉu</p>
            <p class="min-w-[56px] xl:min-w-[114px] text-center">1X2</p>
        </div>';
    }
@endphp

<x-match-schedule::layouts.master :leagues="$leagues" :news="$news" header="Tỷ lệ kèo">
    <div class="js-odds-list-container">
        @if (isset($odds) && count($odds) > 0)
            @foreach ($odds as $index => $season)
                <div class="mb-1">
                    <div data-season-id="{{ $season->season_info->id }}"
                        class="js-toggle-season-btn flex items-center justify-between bg-match-secondary-surface px-4 h-10 {{$index == 0 ? 'border-t' : ''}} border-b border-neutral-150 cursor-pointer">
                        <div class="flex items-center gap-2">
                            <img src="{{ $season->season_info->logo }}" alt="icon-season"
                                class="w-[24px] aspect-square">
                            <div class="text-sm font-medium text-match-primary-content leading-4 max-xl:font-medium">
                                {{ $season->season_info->name }}</div>
                        </div>
                        <div class="flex items-center gap-2.5">
                            <div
                                class="hidden size-6 text-sm font-medium text-match-secondary items-center justify-center js-toggle-season-count rounded bg-match-primary">
                                0
                            </div>
                            <img src="{{  asset('modules/matchschedule/images/icons/arrow-up.svg') }}"
                                alt="icon-arrow-right" class="js-toggle-season-icon w-[24px] h-[24px] z-[1]" />
                        </div>
                    </div>

                    <div data-season-id="{{ $season->season_info->id }}"
                        class="js-season-content active flex flex-col divide-y divide-match-desktop">
                        @foreach ($season->events as $match)
                            <div class="js-match-item">
                                <div class="flex justify-between items-center h-[38px] xl:h-[40px] w-full max-[389px]:px-1 px-4 py-2 xl:pr-3">
                                    <div class="text-sm text-match-secondary">
                                        {{ format_date($match->sport_event->start_time, 'd/m', 'Asia/Ho_Chi_Minh') }}
                                        -
                                        <span
                                            class="text-match-primary-content font-medium">{{ format_date($match->sport_event->start_time, 'H:i', 'Asia/Ho_Chi_Minh') }}</span>
                                    </div>
                                    {!! renderOddsHeader() !!}
                                </div>

                                <div
                                    class="flex items-center gap-2 max-[389px]:px-1 pl-4 pr-[13px] mt-2 max-xl:flex-row-reverse max-xl:justify-between">
                                    <div class="grid grid-cols-[136fr_193fr] gap-2 w-full pb-[17px] ">
                                        <div class="flex flex-col gap-2 overflow-hidden">
                                            {!! renderCompetitor($match->sport_event->competitors[0]) !!}
                                            {!! renderCompetitor($match->sport_event->competitors[1]) !!}
                                        </div>
                                        <div class="flex justify-end gap-2">
                                            <span class="w-[1px] h-full bg-black-5 xl:hidden"></span>
                                            <div class="flex flex-col gap-2">
                                                <div class="flex items-center justify-end gap-2 text-xs leading-4">
                                                    {!! renderOddsBox(MatchSchedule::handicapHome($match)['rate'], MatchSchedule::handicapHome($match)['odds']) !!}
                                                    {!! renderOddsBox(MatchSchedule::overRate($match)['rate'], MatchSchedule::overRate($match)['odds']) !!}
                                                    {!! renderOddsBox('Nhà', MatchSchedule::fullMatch($match)['oh']) !!}
                                                </div>
                                                <div class="flex items-center justify-end gap-2 text-xs leading-4">
                                                    {!! renderOddsBox(MatchSchedule::handicapAway($match)['rate'], MatchSchedule::handicapAway($match)['odds']) !!}
                                                    {!! renderOddsBox(MatchSchedule::underRate($match)['rate'], MatchSchedule::underRate($match)['odds']) !!}
                                                    {!! renderOddsBox('Khách', MatchSchedule::fullMatch($match)['oa']) !!}
                                                </div>
                                                <div class="flex items-center justify-end gap-2 text-xs leading-4">
                                                    <div class="w-[120px] xl:w-[114px] items-center h-[36px] xl:h-[28px]">
                                                        @if ($match->sport_event_status->status == 'not_started')
                                                            <button
                                                                class="js-odds-bet-now-btn w-full xl:w-[114px] items-center h-[32px] xl:h-[28px] text-neutral max-xl:text-sm text-xs font-medium bg-secondary-500 xl:hover:bg-secondary-400 rounded-full"
                                                                data-league-id="{{ $match->odds->li ?? '' }}"
                                                                data-match-id="{{ $match->odds->ei ?? '' }}">
                                                                    Cược Ngay
                                                            </button>
                                                        @endif
                                                    </div>
                                                    {!! renderOddsBox('Hoà', MatchSchedule::fullMatch($match)['od']) !!}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endforeach
        @else
            <x-match-schedule::empty-data />
        @endif
    </div>
</x-match-schedule::layouts.master>
