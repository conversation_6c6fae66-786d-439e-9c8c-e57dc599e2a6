<x-match-schedule::layouts.master :leagues="$leagues" :news="$news" header="Lịch thi đấu">
    <div class="js-match-schedule-list-container">
        @if (count($matchSchedule) > 0)
            @foreach ($matchSchedule as $index => $season)
                <div class="mb-1">
                    <div data-season-id="{{ $season->season_info->id }}"
                        class="js-toggle-season-btn flex items-center justify-between bg-match-secondary-surface px-4 h-10 {{$index == 0 ? 'border-t' : ''}} border-b border-neutral-150 cursor-pointer">
                        <div class="flex items-center gap-2">
                            <img 
                                src="{{ $season->season_info->logo }}" 
                                alt="icon-season"
                                class="w-[24px] aspect-square">
                            <div class="text-sm text-match-primary-content font-medium">{{ $season->season_info->name }}
                            </div>
                        </div>
                        <div class="flex items-center gap-2.5">
                            <div
                                class="hidden size-6 text-sm font-medium text-match-secondary items-center justify-center js-toggle-season-count rounded bg-match-primary">
                                0
                            </div>
                            <img src="{{  asset('modules/matchschedule/images/icons/arrow-up.svg') }}"
                                alt="icon-arrow-right" class="js-toggle-season-icon w-[24px] h-[24px] z-[1]" />
                        </div>
                    </div>

                    <div data-season-id="{{ $season->season_info->id }}" class="js-season-content active">
                        @foreach ($season->events as $match)
                            <div @class([
                                'flex items-center gap-2 p-4 js-match-item h-[88px] max-xl:gap-6',
                                'border-b' => !$loop->last,
                            ])>
                                <div class="flex flex-col min-w-[70px]">
                                    <div class="text-sm text-match-secondary">
                                        {{ format_date($match->sport_event->start_time, 'd/m', 'Asia/Ho_Chi_Minh') }}
                                    </div>
                                    <div class="text-sm text-match-primary-content font-medium">
                                        {{ format_date($match->sport_event->start_time, 'H:i', 'Asia/Ho_Chi_Minh') }}
                                    </div>
                                </div>
                                <div class="flex flex-col gap-2">
                                    <div class="flex items-center gap-2">
                                        <img 
                                            src="{{ $match->sport_event->competitors[0]->logo }}" 
                                            alt="icon-competitor"
                                            class="min-w-[20px] min-h-[20px] max-w-[20px] max-h-[20px]"
                                            onerror="this.onerror=null; this.src='{{  asset('modules/matchschedule/images/tournaments/default.avif') }}';">
                                        <div class="!text-sm text-match-primary-content ">
                                            {{ $match->sport_event->competitors[0]->name }}
                                        </div>
                                    </div>
                                    <div class="flex items-center gap-2">
                                        <img 
                                            src="{{ $match->sport_event->competitors[1]->logo }}"
                                            alt="icon-competitor"
                                            class="min-w-[20px] min-h-[20px] max-w-[20px] max-h-[20px]"
                                            onerror="this.onerror=null; this.src='{{  asset('modules/matchschedule/images/tournaments/default.avif') }}';">
                                        <div class="!text-sm text-match-primary-content ">
                                            {{ $match->sport_event->competitors[1]->name }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endforeach
        @else
            <x-match-schedule::empty-data />
        @endif
    </div>
</x-match-schedule::layouts.master>
