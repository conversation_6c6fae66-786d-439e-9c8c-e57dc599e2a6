@props(['class' => ''])

@php
    $dateFilter = config('matchschedule.date_filter');
    $currentPath = request()->getPathInfo();
    
    // Filter out items based on current path
    if ($currentPath === "/bong-da/ket-qua-bong-da") {
        // Remove item "tomorrow"
        unset($dateFilter['tomorrow']);
    } else {
        // Remove item  "yesterday"
        unset($dateFilter['yesterday']);
    }
@endphp

<div class="date-filter-container flex gap-2 my-4 {{ $class }}">
    @foreach ($dateFilter as $filterLabel => $value)
        <div data-filter-label="{{ $filterLabel }}" 
            data-filter="{{ $value['key'] }}" 
            @class([
                'js-date-filter-item',
                'date-filter-item',
                'active' => request()->get('filterLabel') === $filterLabel || (!request()->get('filterLabel') && $filterLabel == 'all'),
            ])>
                {{ $value['name'] }}
        </div>
    @endforeach
</div>
