@props(['leagues', 'class' => ''])

<div class="relative px-3 search-tournament-container {{ $class }}">
    <label
        class="max-xl:hidden flex items-center gap-[8px] mb-3 h-[40px] w-full pl-[12px] pr-[8px] bg-match-secondary-surface rounded-[8px] border border-transparent [&:has(input:focus)]:border-info-500">
        <img 
            src="{{  asset('modules/matchschedule/images/icons/search-green.svg') }}" 
            alt="icon-search"
            class="w-[20px] h-[20px]">
        <input 
            placeholder="Tìm kiếm"
            class="search-tournament-input pointer-events-none text-ellipsis w-full pr-6 grow text-match-primary-content text-[14px] leading-[20px] font-normal bg-transparent outline-0 placeholder:text-neutral-600" />
            
            <span class="search-tournament-remove absolute top-auto right-[20px] invisible cursor-pointer w-[20px] h-[20px] flex items-center">
                <i class="icon-close text-neutral-600 text-[16px] leading-[16px]"></i>
            </span>
    </label>

    <div class="flex items-center gap-1 xl:hidden relative">
        <div
            class="search-tournament-btn flex justify-between items-center flex-1 h-9 cursor-pointer p-2 bg-match-secondary-surface rounded-lg">
            <div class="js-search-tournament-name text-xs text-match-primary-content leading-[18px] truncate line-clamp-1">
                Chọn Giải Đấu
            </div>
            <img 
                src="{{  asset('modules/matchschedule/images/icons/arrow-down.svg') }}" 
                alt="icon-arrow"
                class="size-5 js-search-tournament-arrow">
        </div>
    </div>

    <div class="tournament-list-blur fixed top-0 left-0 z-[54] hidden w-full h-full bg-black bg-opacity-50 xl:!hidden"></div>

    <div @class([
        'tournament-list-search',
        'xl:z-[9] xl:w-auto xl:h-max xl:max-h-max hidden flex-col px-0 xl:py-2 bg-match-primary xl:rounded-lg xl:shadow-16',
        'xl:absolute xl:top-[40px] xl:left-3 xl:right-3',
        'fixed bottom-0 left-0 w-full z-[55] h-full max-h-[346px] pt-3 pb-[10px] rounded-t-[24px]'
    ])>
        <div class="flex justify-between items-center mb-6 px-4 xl:hidden">
            <p class="text-[14px] leading-[20px] font-medium text-neutral-1000 capitalize">Giải đấu</p>
            <img    
                src="{{ asset('modules/matchschedule/images/tournaments/close-dropdown.avif') }}" 
                alt="close dropdown"
                class="tournament-list-close-dropdown size-8"
            >
        </div>
        <div class="w-full px-4 mb-3 xl:hidden">
            <label
                class="relative flex items-center gap-[8px] h-9 w-full px-2 bg-neutral-50 rounded-[8px] border border-transparent [&:has(input:focus)]:border-info-500">
                <img 
                    src="{{  asset('modules/matchschedule/images/icons/search-green.svg') }}" 
                    alt="icon-search"
                    class="w-[20px] h-[20px]"
                >
                <input 
                    placeholder="Tìm kiếm"
                    class="search-tournament-input pointer-events-none text-ellipsis w-full pr-[28px] grow text-[14px] leading-[20px] font-normal bg-transparent outline-0 text-neutral-1000" 
                />

                <img 
                    src="{{  asset('modules/matchschedule/images/tournaments/close-search.avif') }}" 
                    alt="close-search"
                    class="search-tournament-remove absolute top-auto right-2 invisible cursor-pointer w-5 h-5"
                >
            </label>
        </div>
        <div class="h-[calc(100%-104px)] pr-[14px] pl-4 xl:h-max xl:px-0">
            <div class="tournaments-container flex flex-col h-full overflow-y-auto tournaments-custom-scrollbar xl:h-max xl:max-h-[396px]">
                @if (count($leagues) > 0) 
                    <div 
                        data-seasons="all" 
                        @class([
                            'js-tournament-item tournament-item tournament-item-search xl:hidden tournament-item-all',
                            'flex items-center gap-2 min-h-[44px] cursor-pointer xl:pl-3 xl:pr-2 xl:hover:bg-match-secondary-surface',
                            'active' => !request()->seasons || request()->seasons == 'all',
                        ])
                    >
                        <img 
                            src="{{  asset('modules/matchschedule/images/tournaments/all.avif') }}" 
                            alt="Tất cả"
                            loading="lazy"
                            onerror="this.onerror=null; this.src='{{  asset('modules/matchschedule/images/tournaments/default.avif') }}';"
                            class="w-[20px] h-[20px] object-cover"
                        >
                        <span class="text-[12px] leading-[18px] text-neutral-850 font-medium capitalize xl:text-[14px] xl:leading-[20px]">Tất cả</span>
                    </div>
                    <div class="tournament-list-search-list flex flex-col">
                        @foreach ($leagues as $league)
                            @if (isset($league->id) && $league->is_active)
                                <div 
                                    key="{{ $league->id }}" 
                                    data-seasons="{{ $league->id }}" 
                                    data-name="{{ $league->name }}" 
                                    @class([
                                        'js-tournament-item tournament-item tournament-item-search',
                                        'flex items-center gap-2 h-[44px] cursor-pointer xl:pl-3 xl:pr-2 xl:hover:bg-match-secondary-surface',
                                        'active' => request()->seasons == $league->id,
                                    ])
                                >
                                    <img 
                                        src="{{ $league->logo }}" 
                                        alt="{{ $league?->name ?? '' }}" 
                                        loading="lazy"
                                        class="w-[20px] h-[20px] object-cover"
                                        onerror="this.onerror=null; this.src='{{ asset('modules/matchschedule/images/tournaments/default.avif') }}';"
                                    >
                                    <span class="text-[12px] leading-[18px] text-neutral-850 font-medium xl:text-[14px] xl:leading-[20px]">{{ $league->name }}</span>
                                </div>
                            @endif
                        @endforeach
                    </div>
                @endif

                <div class="tournaments-list-empty w-full h-full flex-col justify-center items-center gap-2 px-3 py-4 xl:py-2 {{ count($leagues) > 0 ? "hidden" : "flex" }}">
                    <img src="/modules/matchschedule/images/tournaments/empty-icon.avif" alt="icon-search" class="w-[60px] h-[60px]">
                    <div class="text-[12px] leading-[18px] text-neutral-850">Không tìm thấy giải đấu.</div>
                </div>
            </div>
        </div>
    </div>
</div>

@pushOnce('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.querySelector('.search-tournament-input');
            if (searchInput) {
                searchInput.classList.remove('pointer-events-none');
            }
        });
    </script>
@endPushOnce