@props(['class' => ''])

@php
    $top_menu = config('matchschedule.top_menu');
    $current_url = request()->path();
@endphp

<div class="sticky top-[86px] z-10 w-dvw h-max overflow-auto no-scrollbar xl:static xl:w-full">
    <div @class([
        'flex gap-2 js-top-menu-container',
        'w-max h-full px-3 pb-3 bg-match-primary items-end xl:px-0 xl:pb-0',
        $class,
    ])>
        @foreach ($top_menu as $key => $menu)
        @php
            $isActive = str_contains( $menu['url'], $current_url);
        @endphp
            <div 
                data-url="{{ $menu['url'] }}" 
                @class(['top-menu-item cursor-pointer', 'active js-active-top-menu' => $isActive, '!min-w-[160px]' => $key == 'rankings'])}
            >
                <div class="flex items-center justify-center gap-[4px] w-max">
                    <img 
                        src="{{  asset('modules/matchschedule/images/icons/' . $menu['icon']) }}" 
                        alt="icon-menu"
                        class="w-[20px] h-[20px]">
                    <span class="text-sm font-normal text-nowrap">{{ $menu['name'] }}</span>
                </div>
            </div>
        @endforeach
    </div>
</div>

@pushOnce('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelector('.js-top-menu-container').addEventListener('click', function(e) {
                const item = e.target.closest('.top-menu-item');
                if (item) {
                    const url = item.getAttribute('data-url');
                    const currentSearch = new URLSearchParams(window.location.search);
                    const filterLabel = currentSearch.get('filterLabel');
                    
                    if ((filterLabel === 'tomorrow' && url === '/bong-da/ket-qua-bong-da') || filterLabel === 'yesterday' || url !== "/bong-da/bang-xep-hang-bong-da") {
                        currentSearch.delete('date');
                        currentSearch.set('filterLabel', 'all');
                        currentSearch.set('filter', 'all');
                        const newSearch = currentSearch.toString();
                        window.location.href = url + (newSearch ? '?' + newSearch : '');
                        return;
                    }

                    window.location.href = url + window.location.search;
                    
                }
            });
        });
    </script>
@endPushOnce