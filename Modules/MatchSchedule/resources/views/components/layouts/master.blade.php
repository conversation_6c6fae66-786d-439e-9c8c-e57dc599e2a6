@props(['leagues', 'news', 'header' => '', 'isShowDateFilter' => true])

@push('sass')
    @vite('Modules/MatchSchedule/resources/assets/sass/app.scss')
@endpush

<x-layout
    class=" bg-match-desktop max-xl:bg-match-primary">
    <div class="max-w-[1240px] mx-auto xl:mt-4 pb-[74px] xl:pb-[80px]">
        <x-match-schedule::breadcrumb
            :list="[['name' => $header, 'url' => '#']]"
            class="[&>a]:leading-[18px] max-xl:px-3"
        />

        <div class="mt-0 xl:mt-4 w-full flex gap-4 xl:gap-5 max-xl:flex-col">
            <div class="w-full h-fit xl:w-[231px] shadow-4 bg-match-primary rounded-lg py-3 max-xl:hidden">
                <x-match-schedule::tournaments :leagues="$leagues"></x-match-schedule::tournaments>
            </div>

            <div class="flex-1 xl:shadow-32 xl:min-w-[674px] bg-match-primary rounded-lg pb-3 xl:p-4">
                <x-match-schedule::top-menu class=""></x-match-schedule::top-menu>

                <x-match-schedule::search-tournamets :leagues="$leagues" class="xl:hidden mb-3 !px-3" />

                @if ($isShowDateFilter)
                    <x-match-schedule::date-filter class="px-3 max-xl:!mt-3 xl:mt-4 xl:px-0" />
                @endif

                <div class="js-content-loading w-full h-fit hidden">
                    <x-match-schedule::loading />
                </div>

                <div class="js-content-container px-3 xl:px-0">
                    {{ $slot }}
                </div>
            </div>

            <div class="w-full bg-match-primary rounded-lg xl:w-[295px] h-fit space-y-4 xl:space-y-0">
                <x-match-schedule::tags class="max-xl:px-3"></x-match-schedule::tags>
                <x-match-schedule::news 
                    class="max-xl:w-screen"
                    :news="$news">
                </x-match-schedule::news>
            </div>
        </div>
    </div>

    @pushOnce('scripts')
        @vite('Modules/MatchSchedule/resources/assets/js/app.js')
    @endpushOnce

    @pushOnce('scripts')
        @vite('Modules/MatchSchedule/resources/assets/js/helper.js')
    @endpushOnce
</x-layout>

<script>
    const leagues = {!! json_encode($leagues) !!};
    window.globalMatchScheduleLeagues = leagues;
</script>
