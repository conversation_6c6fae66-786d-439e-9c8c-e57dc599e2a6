@props(['news', 'class' => '', 'headerClass' => '', 'listClass' => ''])

@php
    use App\Enums\UrlPathEnum;
    $topPosts = isset($news->posts) ? $news->posts : [];
    $topPostsSwiperConfig = [
        'slidesPerView' => 2.08,
        'spaceBetween' => 6,
    ];
@endphp

@if (count($topPosts) > 0)
<div class="{{ $class }}">
    {{-- PC --}}
    <div class="hidden xl:block">
        <div class="flex justify-center py-[10px] bg-neutral-250">
            <p class="text-[16px] leading-[24px] font-medium text-primary">TIN TỨC MỚI NHẤT</p>
        </div>
        <div class="[&_.card-image_img]:xl:aspect-[295/168]">
            @foreach ($topPosts as $index => $item)
                @if ($index === 0)
                    <x-ui.news.card-news 
                        direction="vertical" 
                        :data="$item" 
                        isHiddenDescription 
                        class="rounded-none">
                    </x-ui.news.card-news>
                @else
                    <x-ui.news.card-news 
                        direction="vertical" 
                        :data="$item" 
                        isHiddenImageAndTime 
                        isHiddenDescription
                        class="h-[72px] rounded-none border-t border-neutral-150 [&_.card-title]:text-[14px] [&_.card-title]:leading-[20px]">
                    </x-ui.news.card-news>
                @endif
            @endforeach
        </div>
    </div>

    {{-- MB --}}
    <div class="xl:hidden w-full">
        <div class="w-full flex justify-between px-3">
            <p class="text-match-primary-content text-sm font-medium">Tin tức mới nhất</p>
            <x-kit.link-button 
                class="text-[10px] leading-[14px] text-match-secondary"
                href="/tin-tuc" 
                style='light'>
                    Xem thêm
            </x-kit.link-button>
        </div>

        <div class="w-full mt-3 pl-3">
            <x-kit.swiper :swiperConfig="$topPostsSwiperConfig" swiperRequiredClass="category-top-posts-swiper">
                @foreach ($topPosts as $item)
                    <div class="swiper-slide">
                        <x-ui.news.card-news 
                            isHiddenDescription 
                            direction="vertical" 
                            :data="$item">
                        </x-ui.news.card-news>
                    </div>
                @endforeach
            </x-kit.swiper>
        </div>
    </div>
</div>

@endif
