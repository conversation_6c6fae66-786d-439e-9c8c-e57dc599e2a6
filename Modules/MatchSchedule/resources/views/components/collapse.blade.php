@props(['id' => '', 'name' => '', 'logo' => '', 'active' => true])

<div>
    <div
        data-season-id="{{ $id }}"
        class="js-toggle-season-btn flex items-center justify-between bg-match-desktop px-4 h-10 border-b border-t border-match-primary-50 cursor-pointer"
    >
        <div class="flex items-center gap-2">
            <img src="{{ $logo }}" alt="icon-season" class="w-[24px] aspect-square">
            <div class="text-xs leading-4 max-xl:font-medium">{{ $name }}</div>
        </div>
        <div class="w-[24px] h-[24px] rounded bg-neutral-600">
            <img 
                src="{{  asset('modules/matchschedule/images/icons/arrow-up.svg') }}"
                alt="icon-arrow-right"
                class="js-toggle-season-icon w-[24px] h-[24px] z-[1]"
            />
        </div>
    </div>

    <div 
        data-season-id="{{ $id }}"
        class="js-season-content bg-neutral-700 {{ $active ? 'active' : '' }}"
    >
        {{ $slot }}
    </div>
</div>