@props(['class' => ''])

@php
    use App\Helpers\DetectDeviceHelper;
    $tags = config('matchschedule.tags');

    $isMobile = DetectDeviceHelper::isMobile();
    
@endphp

<div class="{{ $class }}">
    <div class="hidden xl:flex justify-center items-center h-[44px] bg-neutral-250 border-b border-match-desktop">
        <span class="text-match-primary-content font-medium text-base">
            TAGS
        </span>
    </div>

    <div class="flex flex-wrap gap-2 p-4 xl:border-none border rounded-lg border-match-desktop">
        @foreach ($tags as $tag)
            <a 
                href="{{ $isMobile ? $tag['mobileUrl'] : $tag['url'] }}" 
                class="px-[10px] py-[8px] text-[12px]/[18px] text-match-secondary rounded xl:hover:bg-primary-50 xl:hover:text-neutral-1000"
            >
                #{{ $tag['name'] }}
            </a>
        @endforeach
    </div>
</div>
