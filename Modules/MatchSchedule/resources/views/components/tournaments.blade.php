@props(['leagues'=>[]])

<div class="tournaments-container">
    <x-match-schedule::search-tournamets :leagues="$leagues" />

    @if (isset($leagues) && count($leagues) > 0)
        <div class="flex flex-col">
            <div
                data-seasons="all"
                @class([
                    'js-tournament-item tournament-item',
                    'flex items-center gap-2 h-[44px] px-3 py-2 cursor-pointer hover:bg-match-secondary-surface',
                    'active' => !request()->seasons || request()->seasons == 'all',
                ])
            >
                <img
                    src="{{  asset('modules/matchschedule/images/tournaments/all.avif') }}"
                    alt="Tất cả"
                    loading="lazy"
                    onerror="this.onerror=null; this.src='{{  asset('modules/matchschedule/images/tournaments/default.avif') }}';"
                    class="min-w-[20px] min-h-[20px] max-w-[20px] max-h-[20px] object-contain"
                >
                <span class="text-sm font-medium text-match-secondary">T<PERSON><PERSON></span>
            </div>
            
            @foreach ($leagues as $league)
                @if (isset($league->id) && $league->is_active)
                    <div 
                        key="{{ $league->id }}"
                        data-seasons="{{ $league->id }}"
                        @class([
                            'js-tournament-item tournament-item',
                            'flex items-center gap-2 h-[44px] px-3 py-2 cursor-pointer text-match-secondary hover:bg-match-secondary-surface',
                            'active' => request()->seasons == $league->id,
                        ])
                    >
                        <img
                            src="{{ $league->logo }}"
                            alt="{{ $league?->name ?? '' }}"
                            loading="lazy"
                            class="min-w-[20px] min-h-[20px] max-w-[20px] max-h-[20px] object-contain"
                            onerror="this.onerror=null; this.src='{{  asset('modules/matchschedule/images/tournaments/default.avif') }}';"
                        >
                        <span class="text-sm font-medium">{{ $league->name }}</span>
                    </div>
                @endif
            @endforeach
        </div>
    @endif
</div>
