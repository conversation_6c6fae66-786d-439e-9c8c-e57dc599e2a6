@props([ 'list' => [], 'class' => '', 'itemClass' => ''])

<div class="breadcrumb flex items-center gap-1.5 text-neutral-850 max-xl:pt-3 max-xl:pb-2 max-xl:sticky max-xl:top-12 max-xl:z-[10] max-xl:bg-match-primary text-xs/[18px] {{ $class }}">
    <a href="/" class="">
        Trang chủ
    </a>
    @foreach ($list as $key => $item)
        <div class=""> / </div>
        @if (isset($item['url']) && $item['url'] != '#')
            <a href="{{ $item['url'] }}" @class([
                $itemClass,
                'text-neutral-850' => $key === count($list) - 1,
            ])>
                {{ $item['name'] }}
            </a>
        @else
            <p class="text-neutral-1000">
                {{ $item['name'] }}
            </p>
        @endif
    @endforeach
</div>
