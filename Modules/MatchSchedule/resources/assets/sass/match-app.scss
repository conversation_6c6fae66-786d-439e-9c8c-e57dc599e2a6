.tournaments-container {
    .tournament-item.active {
        span {
            @apply text-match-brand-primary-light #{!important};
        }
    }
}

.top-menu-item {
    @apply min-w-[130px] xl:min-w-max xl:px-[20px] flex items-center justify-center rounded-[8px]
    bg-match-secondary-surface h-[32px] xl:h-[40px] text-match-secondary;

    img {
        filter: brightness(0) saturate(100%) invert(67%) sepia(11%) 
        saturate(252%) hue-rotate(183deg) brightness(91%) contrast(89%);
    }
            
    @media (min-width: 1200px) {
        &:hover {
                @apply text-primary-700;

                img {
                    filter: brightness(0) saturate(100%) invert(41%) sepia(77%) 
                    saturate(312%) hue-rotate(0deg) brightness(86%) contrast(94%);
                }
        }
    }

    &.active {
        @apply bg-primary-500 xl:hover:bg-primary-400 text-neutral;
        
        img {
            filter: brightness(0) saturate(100%) invert(99%) sepia(13%) 
                saturate(424%) hue-rotate(233deg) brightness(118%) contrast(100%);
        }
    }
}


.date-filter-container {
    .date-filter-item {
        @apply xl:px-[12px] h-[32px] w-full xl:w-fit max-w-[85px] xl:max-w-[86px] flex items-center justify-center border-none
          rounded-md text-match-secondary bg-match-secondary-surface cursor-pointer text-nowrap text-sm;

        @media (min-width: 1200px) {
            &:hover {
                @apply text-primary-700;
            }
        }
    
        &.active {
            @apply border-[1px] xl:px-[11px] border-solid bg-neutral border-primary-500 font-medium text-primary-700;
        }
    }
}