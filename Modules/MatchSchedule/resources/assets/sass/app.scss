@import "./match-app.scss";

.scrollbar-hide {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
    display: none;  /* Chrome, Safari and Opera */
} 

.js-toggle-season-icon {
    transition: transform 0.3s ease;
}

.js-season-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-out;
}

.js-season-content.active {
    max-height: 100%;
}

.tournaments-custom-scrollbar::-webkit-scrollbar {
    width: 4px;
    height: 4px;
}

.tournaments-custom-scrollbar::-webkit-scrollbar-track {
    border-radius: 9999px;
}

.tournaments-custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: #AAAEB780;
    border-radius: 9999px;
    background-clip: padding-box;
}

.tournaments-custom-scrollbar::-webkit-scrollbar-button {
    display: none;
}

@media (min-width: 1200px) {
    .tournaments-custom-scrollbar::-webkit-scrollbar {
        width: 4px;
        height: 4px;
        margin-top: 4px;
    }
}

