const DEFAULT_BETTING_RATE = "-";

window.getHandicap = (item, index, homeKey) => {
    const handicap = item?.odds?.m?.[index]?.o?.[0];
    if (handicap) {
        return {
            odds: handicap[homeKey]?.ma ?? DEFAULT_BETTING_RATE,
            rate:
                homeKey === "oh"
                    ? handicap?.p ?? 0
                    : (-parseFloat(handicap?.p)).toString() ??
                      DEFAULT_BETTING_RATE,
        };
    }
    return {
        odds: DEFAULT_BETTING_RATE,
        rate: DEFAULT_BETTING_RATE,
    };
};

window.getFullMatch = (item, index = 2) => {
    const handicap = item?.odds?.m?.[index]?.o?.[0];
    if (handicap) {
        return {
            oh: handicap?.oh?.de ?? DEFAULT_BETTING_RATE,
            oa: handicap?.oa?.de ?? DEFAULT_BETTING_RATE,
            od: handicap?.od?.de ?? DEFAULT_BETTING_RATE,
        };
    }
    return {
        oh: DEFAULT_BETTING_RATE,
        oa: DEFAULT_BETTING_RATE,
        od: DEFAULT_BETTING_RATE,
    };
};

window.getCard = (item, index) => {
    const _info = item.statistics?.totals?.competitors[index];
    if (!_info) {
        return null;
    }
    return {
        yellow_cards: Number(_info?.statistics?.yellow_cards) || 0,
        red_cards: Number(_info?.statistics?.red_cards) || 0,
        corner_kicks: Number(_info?.statistics?.corner_kicks) || 0,
    };
};

window.handicapHome = (item) => {
    return getHandicap(item, 0, "oh");
};

window.handicapAway = (item) => {
    return getHandicap(item, 0, "oa");
};

window.overRate = (item) => {
    return getHandicap(item, 1, "oh");
};

window.underRate = (item) => {
    return getHandicap(item, 1, "oa");
};

window.fullMatch = (item) => {
    return getFullMatch(item, 2);
};

window.checkNumber = (number) => {
    if (number > 0) {
        return "status--up";
    }
    if (number < 0) {
        return "status--down";
    }
    return "";
};

window.formatDateUTC = (value) => {
    return convertTimeUTC(value).format("DD/MM");
};

window.formatTimeUTC = (value) => {
    return convertTimeUTC(value).format("HH:mm");
};

window.formatDateUTCOdds = (value) => {
    return convertTimeUTC(value).format("DD/MM - HH:mm");
};

// Rankings outcome
window.convertOutcome = (outcome, isNote = false) => {
    switch (outcome) {
        case "Champions League":
        case "Qualified":
        case "Champions League league stage":
        case "UEFA CL group stage":
        case "Semifinal":
        case "Title Play-offs":
        case "LIBC CL group stage":
        case "Promotion":
        case "Upgrade Team":
        case "AFC Champions League Elite Group Stage":
        case "Knockout Phase":
            return "#4286F5";
        case "Champions League Qualification":
        case "Qualification Playoffs":
        case "Playoffs":
        case "UEFA ECL Playoffs":
        case "UEFA ECL play-offs":
        case "Promotion Playoff":
        case "Promotion Playoffs":
        case "Upgrade Play-off":
        case "Upgrade Play-offs":
        case "UEFA ECL qualifying playoffs":
        case "Playoff playoffs":
        case "UEFA ECL Qualification":
        case "UEFA ECL offs":
        case "AFC Champions League Elite Playoff":
            return "#F97A16";
        case "UEFA Europa League":
        case "Europa League league stage":
        case "Play Offs: Quarter-finals":
        case "UEFA EL Qualification":
        case "UEFA EL play-offs":
        case "AFC Champions League 2 Group Stage":
            return "#0DAB1D";
        case "Conference League Qualification":
        case "UEFA qualifying":
        case "UEFA  qualifying":
        case "UEFA CL play-offs":
            return "#24C1E0";
        case "Relegation Playoffs":
        case "Relegation Play-offs":
            return "#FFC659";
        case "Relegation":
        case "Degrade Team":
        case "Degrade  Team":
            return "#D01C2D";
        default:
            return isNote ? "#0DAB1D" : outcome;
    }
};

window.convertNameLeague = (name) => {
    switch (name) {
        case "Playoffs":
            return "Vòng đấu loại trực tiếp";
        case "Qualified":
            return "Vòng tiếp theo";
        case "Europa League league stage":
            return "Vòng bảng Europa League";
        case "Champions League league stage":
            return "Vòng bảng Champions League";
        case "UEFA ECL Playoffs":
            return "Vòng loại trực tiếp UEFA ECL";
        case "Champions League":
            return "Vòng bảng Vô địch các CLB Châu Âu";
        case "Champions League Qualification":
            return "Vòng loại Vô địch các CLB Châu Âu";
        case "UEFA Europa League":
            return "Vòng bảng UEFA Europa";
        case "Conference League Qualification":
            return "Vòng loại UEFA Europa Conference";
        case "Relegation":
            return "Xuống hạng";
        case "Relegation Playoffs":
            return "Trận quyết định đội xuống hạng";
        case "Promotion":
            return "Thăng hạng";
        case "Promotion Playoffs":
            return "Trận quyết định đội thăng hạng";
        default:
            return name;
    }
};

window.completedOutCome = (groups) => {
    let result = [];
    groups.forEach((group) => {
        if (group.current_outcome?.length > 0) {
            result = result.concat(group.current_outcome);
        }
    });
    return [...new Set(result)];
};

// Results
window.matchStatusMap = {
    not_started: {
        label: "Chưa diễn ra",
        color: "#16A34A",
        border: "#BBF7D0",
    },
    ended: {
        label: "Kết thúc",
        color: "#575D6A",
        border: "#CFD1D9",
    },
    live: {
        label: "Đang diễn ra",
        color: "#1BA6AC",
        border: "#BBE4E6",
    },
    cancelled: {
        label: "Huỷ",
        color: "#DC2626",
        border: "#FECACA",
    },
    postponed: {
        label: "Tạm hoãn",
        color: "#E09810",
        border: "#FEF08A",
    },
};

window.getMatchStatus = (status) => {
    return matchStatusMap[status] ?? matchStatusMap["not_started"];
};

// ========== Empty content ==========
window.emptyContentRendering = () => `
<div class="flex flex-col items-center justify-center w-full h-[525px] xl:h-[400px] ">
    <img src="/modules/matchschedule/images/icons/empty.svg" alt="icon-no-data" class="size-[100px] max-xl:size-[68px]">
    <div class="text-center text-sm max-xl:text-xs text-match-secondary mt-4">Không có trận đấu</div>
</div>
`;

// ========== Match schedule content ==========
window.matchScheduleContentRendering = (season,index) => `
    <div class="mb-1">
        <div
            data-season-id="${season.season_info.id}"
            class="js-toggle-season-btn flex items-center justify-between bg-match-secondary-surface px-4 h-10 border-b border-neutral-150 cursor-pointer ${index == 0 ? 'border-t' : ''}"
        >
            <div class="flex items-center gap-2">
                <img src="${season.season_info.logo}" alt="icon-season"
                    class="w-[24px] aspect-square">
                <div class="text-sm text-match-primary-content font-medium">${season.season_info.name}</div>
            </div>
            <div class="flex items-center gap-2.5">
                <div class="hidden size-6 text-sm font-medium text-match-secondary items-center justify-center js-toggle-season-count rounded bg-match-primary">0</div>
                <img src="/modules/matchschedule/images/icons/arrow-up.svg"
                    alt="icon-arrow-right" class="js-toggle-season-icon w-[24px] h-[24px] z-[1]" />
            </div>
        </div>
        <div data-season-id="${season.season_info.id}" class="js-season-content active">
            ${season.events.map((match, index) => `
                <div class="flex items-center gap-2 p-4 h-[88px] js-match-item max-xl:gap-6 ${index !== season.events.length - 1 ? "border-b" : ""}">
                    <div class="flex flex-col min-w-[70px]">
                        <div class="text-sm text-match-secondary">
                            ${formatDateTime(match.sport_event.start_time, "DD/MM")}
                        </div>
                        <div class="text-sm text-match-primary-content font-medium">
                            ${formatDateTime(match.sport_event.start_time, "HH:mm")}
                        </div>
                    </div>
                    <div class="flex flex-col gap-2">
                        <div class="flex items-center gap-2">
                            <img src="${match.sport_event.competitors[0].logo}" alt="icon-competitor"
                                class="min-w-[20px] min-h-[20px] max-w-[20px] max-h-[20px]"
                                onerror="this.onerror=null; this.src='/modules/matchschedule/images/tournaments/default.avif';">
                            <div class="!text-sm text-match-primary-content ">
                                ${match.sport_event.competitors[0].name}
                            </div>
                        </div>
                        <div class="flex items-center gap-2">
                            <img src="${match.sport_event.competitors[1].logo}" alt="icon-competitor"
                                class="min-w-[20px] min-h-[20px] max-w-[20px] max-h-[20px]"
                                onerror="this.onerror=null; this.src='/modules/matchschedule/images/tournaments/default.avif';">
                            <div class="!text-sm text-match-primary-content ">
                                ${match.sport_event.competitors[1].name}
                            </div>
                        </div>
                    </div>
                </div>
            `).join("")}
        </div>
    </div>
`;

// ========== Odds content ==========
window.oddsContentRendering = (season,index) => `
    <div class="mb-1">
        <div
            data-season-id="${season.season_info.id}"
            class="js-toggle-season-btn flex items-center justify-between bg-match-secondary-surface px-4 h-10 border-b border-neutral-150 cursor-pointer ${index == 0 ? 'border-t' : ''}"
        >
            <div class="flex items-center gap-2">
                <img src="${season.season_info.logo}" alt="icon-season"
                    class="w-[24px] aspect-square">
                <div class="text-sm font-medium text-match-primary-content leading-4 max-xl:font-medium">
                    ${season.season_info.name}
                </div>
            </div>
            <div class="flex items-center gap-2.5">
                <div class="hidden size-6 text-sm font-medium text-match-secondary items-center justify-center js-toggle-season-count rounded bg-match-primary">0</div>
                <img src="/modules/matchschedule/images/icons/arrow-up.svg"
                    alt="icon-arrow-right" class="js-toggle-season-icon w-[24px] h-[24px] z-[1]" />
            </div>
        </div>
        <div data-season-id="${season.season_info.id}"
            class="js-season-content active flex flex-col divide-y divide-match-desktop">
            ${season.events.map((match) => `
                <div class="js-match-item">
                    <div class="flex justify-between w-full h-[38px] xl:h-[40px] max-[389px]:px-1 px-4 py-2 xl:pr-3">
                        <div class="text-sm text-match-secondary">
                            ${formatDateTime(match.sport_event.start_time, "DD/MM")}
                            -
                            <span class="text-match-primary-content font-medium">${formatDateTime(match.sport_event.start_time, "HH:mm")}</span>
                        </div>
                        <div class="flex items-center gap-2 text-xs xl:text-sm text-match-secondary">
                            <p class="min-w-[56px] xl:min-w-[114px] text-center">Kèo chấp</p>
                            <p class="min-w-[56px] xl:min-w-[114px] text-center">Tài xỉu</p>
                            <p class="min-w-[56px] xl:min-w-[114px] text-center">1X2</p>
                        </div>
                    </div>
                    <div class="flex items-center gap-2 max-[389px]:px-1 pl-4 pr-[13px] mt-2 max-xl:flex-row-reverse max-xl:justify-between">
                        <div class="grid grid-cols-[136fr_193fr] gap-2 w-full pb-[17px] ">
                            <div class="flex flex-col gap-2 overflow-hidden">
                                <div class="flex items-center gap-2">
                                    <img src="${match.sport_event.competitors[0].logo}" alt="icon-competitor"
                                        class="min-w-[20px] min-h-[20px] max-w-[20px] max-h-[20px] text-match-primary-content"
                                        onerror="this.onerror=null; this.src='/modules/matchschedule/images/tournaments/default.avif';">
                                    <div class="!text-sm max-xl:!text-xs !leading-[18px] text-match-primary-content max-xl:w-[108px] w-fit flex-1 xl:w-[193px] truncate line-clamp-1"
                                        title="${match.sport_event.competitors[0].name}">
                                        ${match.sport_event.competitors[0].name}
                                    </div>
                                </div>
                                <div class="flex items-center gap-2">
                                    <img src="${match.sport_event.competitors[1].logo}" alt="icon-competitor"
                                        class="min-w-[20px] min-h-[20px] max-w-[20px] max-h-[20px] text-match-primary-content"
                                        onerror="this.onerror=null; this.src='/modules/matchschedule/images/tournaments/default.avif';">
                                    <div class="!text-sm max-xl:!text-xs !leading-[18px] text-match-primary-content max-xl:w-[108px] w-fit flex-1 xl:w-[193px] truncate line-clamp-1"
                                        title="${match.sport_event.competitors[1].name}">
                                        ${match.sport_event.competitors[1].name}
                                    </div>
                                </div>
                            </div>
                            <div class="flex justify-end gap-2">
                                <span class="w-[1px] h-full bg-black-5 xl:hidden"></span>
                                <div class="flex flex-col gap-2 max-xl:w-[184px]">
                                    <div class="flex items-center justify-end gap-2 text-xs leading-4">
                                        <div class="flex justify-between w-[56px] xl:w-[114px] items-center h-[32px] max-xl:text-[10px]/[14px] xl:h-[28px] px-3 max-xl:py-0.5 py-1 text-center bg-neutral-100 rounded-lg max-xl:flex-col">
                                            <div class="text-match-secondary max-xl:text-[10px]/[14px]">
                                                ${window.handicapHome(match)["rate"]}
                                            </div>
                                            <div class="text-match-primary-content text-xs font-medium max-xl:text-[10px]/[14px]">
                                                ${window.handicapHome(match)["odds"]}
                                            </div>
                                        </div>
                                        <div class="flex justify-between w-[56px] xl:w-[114px] items-center h-[32px] max-xl:text-[10px]/[14px] xl:h-[28px] px-3 max-xl:py-0.5 py-1 text-center bg-neutral-100 rounded-lg max-xl:flex-col">
                                            <div class="text-match-secondary max-xl:text-[10px]/[14px]">
                                                ${window.overRate(match)["rate"]}
                                            </div>
                                            <div class="text-match-primary-content text-xs font-medium max-xl:text-[10px]/[14px]">
                                                ${window.overRate(match)["odds"]}
                                            </div>
                                        </div>
                                        <div class="flex justify-between w-[56px] xl:w-[114px] items-center h-[32px] max-xl:text-[10px]/[14px] xl:h-[28px] px-3 max-xl:py-0.5 py-1 text-center bg-neutral-100 rounded-lg max-xl:flex-col">
                                            <div class="text-match-secondary">Nhà</div>
                                            <div class="text-match-primary-content text-xs font-medium max-xl:text-[10px]/[14px]">
                                                ${window.fullMatch(match)["oh"]}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="flex items-center justify-end gap-2 text-xs leading-4">
                                        <div class="flex justify-between w-[56px] xl:w-[114px] items-center h-[32px] max-xl:text-[10px]/[14px] xl:h-[28px] px-3 max-xl:py-0.5 py-1 text-center bg-neutral-100 rounded-lg max-xl:flex-col">
                                            <div class="text-match-secondary max-xl:text-[10px]/[14px]">
                                                ${window.handicapAway(match)["rate"]}
                                            </div>
                                            <div class="text-match-primary-content text-xs font-medium max-xl:text-[10px]/[14px]">
                                                ${window.handicapAway(match)["odds"]}
                                            </div>
                                        </div>
                                        <div class="flex justify-between w-[56px] xl:w-[114px] items-center h-[32px] max-xl:text-[10px]/[14px] xl:h-[28px] px-3 max-xl:py-0.5 py-1 text-center bg-neutral-100 rounded-lg max-xl:flex-col">
                                            <div class="text-match-secondary max-xl:text-[10px]/[14px]">
                                                ${window.underRate(match)["rate"]}
                                            </div>
                                            <div class="text-match-primary-content text-xs font-medium max-xl:text-[10px]/[14px]">
                                                ${window.underRate(match)["odds"]}
                                            </div>
                                        </div>
                                        <div class="flex justify-between w-[56px] xl:w-[114px] items-center h-[32px] max-xl:text-[10px]/[14px] xl:h-[28px] px-3 max-xl:py-0.5 py-1 text-center bg-neutral-100 rounded-lg max-xl:flex-col">
                                            <div class="text-match-secondary">Khách</div>
                                            <div class="text-match-primary-content text-xs font-medium max-xl:text-[10px]/[14px]">
                                                ${window.fullMatch(match)["oa"]}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="flex items-center justify-end gap-2 text-xs leading-4">
                                        <div class="w-[120px] xl:w-[114px] items-center h-[36px] xl:h-[28px]">
                                            ${match.sport_event_status.status == "not_started"
                                                ? `<button class="js-odds-bet-now-btn w-full xl:w-[114px] items-center h-[32px] xl:h-[28px] text-neutral max-xl:text-sm text-xs font-medium bg-secondary-500 xl:hover:bg-secondary-400 rounded-full"
                                                    data-league-id="${match?.odds?.li ?? ''}"
                                                    data-match-id="${match?.odds?.ei ?? ''}">
                                                    Cược Ngay
                                                </button>`
                                                : ""
                                            }
                                        </div>
                                        <div class="flex justify-between w-[56px] xl:w-[114px] items-center h-[32px] max-xl:text-[10px]/[14px] xl:h-[28px] px-3 max-xl:py-0.5 py-1 text-center bg-neutral-100 rounded-lg max-xl:flex-col">
                                            <div class="text-match-secondary">Hoà</div>
                                            <div class="text-match-primary-content text-xs font-medium max-xl:text-[10px]/[14px]">
                                                ${window.fullMatch(match)["od"]}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `).join("")}
        </div>
    </div>
`;

window.rankingsContentRendering = (rankings) => {
    const isMultipleSeasons = Array.isArray(rankings) && rankings.length > 0;
    const isSingleSeason =
        rankings && typeof rankings === "object" && !Array.isArray(rankings);

    if (isMultipleSeasons) {
        return rankings
            .map(
                (ranking) => `
            <div
                data-season-id="${ranking.season.id}"
                class="js-toggle-season-btn flex items-center  justify-between bg-match-desktop px-4 h-10 xl:p-2 border-b border-t border-neutral-150 cursor-pointer"
            >
                <div class="flex items-center gap-2">
                    <img src="${
                        ranking.season.logo
                    }" alt="icon-season" class="w-[24px] aspect-square">
                    <div class="text-sm text-match-primary-content font-medium">${
                        ranking.season.name
                    }</div>
                </div>
                <div class="w-[24px] h-[24px] ">
                    <img 
                        src="/modules/matchschedule/images/icons/arrow-up.svg"
                        alt="icon-arrow-right"
                        class="js-toggle-season-icon w-[24px] h-[24px] z-[1]"
                    />
                </div>
            </div>

            <div 
                data-season-id="${ranking.season.id}"
                class="js-season-content active mb-1"
            >
                ${
                    ranking.standings.groups.length > 0
                        ? `
                    ${ranking.standings.groups
                        .map(
                            (group) => `
                        <div class="">
                            ${
                                group.id !== 0
                                    ? `
                                <div class="flex items-center h-[40px] text-sm text-match-primary-content font-medium leading-5 max-xl:bg-neutral-790 max-xl:pl-3">
                                    Bảng ${group.group_name}
                                </div>
                            `
                                    : ""
                            }

                            ${
                                group.standings.length > 0
                                    ? `
                                <div class="flex items-center justify-between px-4 max-xl:px-1.5 h-10 xl:h-8 bg-match-secondary-surface max-xl:text-xs text-sm text-match-secondary">
                                    <div class="flex items-center">
                                        <div class="w-[26px] text-center">TT</div>
                                        <div class="w-[76px] xl:w-[120px] ml-[10px] xl:ml-[32px] text-center">Đội
                                        </div>
                                    </div>
                                    <div class="flex items-center justify-between xl:gap-1">
                                        <div class="w-[29px] xl:w-[42.5px] text-center">Trận</div>
                                        <div class="w-[29px] xl:w-[42.5px] text-center">
                                            <span class="xl:hidden">Th</span>
                                            <span class="max-xl:hidden">Thắng</span>
                                        </div>
                                        <div class="w-[29px] xl:w-[42.5px] text-center">
                                            <span class="xl:hidden">H</span>
                                            <span class="max-xl:hidden">Hoà</span>
                                        </div>
                                        <div class="w-[28px] xl:w-[42.5px] text-center">
                                            <span class="xl:hidden">B</span>
                                            <span class="max-xl:hidden">Thua</span>
                                        </div>
                                        <div class="w-[33px] xl:w-[42.5px] text-center">BT</div>
                                        <div class="w-[29px] xl:w-[42.5px] text-center">SBT</div>
                                        <div class="w-[29px] xl:w-[42.5px] text-center">+/-</div>
                                        <div class="w-[30px] xl:w-[42.5px] text-center">Điểm</div>
                                    </div>
                                </div>

                                ${group.standings
                                    .map(
                                        (standing, index) => `
                                    <div class="flex w-full">
                                        <div class="flex flex-1 items-center justify-between px-4 max-xl:px-1.5 max-xl:h-[39px] h-12 text-match-primary-content text-sm border-b border-neutral-150">
                                            <div class="flex items-center">
                                                <div class="w-[26px] text-center relative text-sm">
                                                    ${index + 1}
                                                    <div class="w-6 h-1.5 max-xl:h-[3px] max-xl:w-[16px] max-xl:bottom-[-10px] absolute bottom-[-16px] rounded-t-full max-xl:left-[5px] left-0"
                                                        ${
                                                            standing.current_outcome
                                                                ? `style="background-color: ${window.convertOutcome(
                                                                      standing.current_outcome
                                                                  )};"`
                                                                : ""
                                                        }
                                                    ></div>
                                                </div>
                                                <div class="flex items-center gap-1 w-[76px] xl:w-[140px] ml-[10px] xl:ml-[32px]">
                                                    <img src="${
                                                        standing.competitor.logo
                                                    }" alt="logo-competitor" class="w-[20px] h-[20px] bg-neutral rounded-[2px]">
                                                    <div class="truncate line-clamp-1 max-xl:text-xs" title="${
                                                        standing.competitor.name
                                                    }">
                                                        ${
                                                            standing.competitor
                                                                .name
                                                        }
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="flex items-center justify-between xl:gap-1 max-xl:text-xs">
                                                <div class="w-[29px] xl:w-[42.5px] text-center">
                                                    ${standing.played ?? ""}
                                                </div>
                                                <div class="w-[29px] xl:w-[42.5px] text-center">
                                                    ${standing.win ?? ""}
                                                </div>
                                                <div class="w-[29px] xl:w-[42.5px] text-center">
                                                    ${standing.draw ?? ""}
                                                </div>
                                                <div class="w-[28px] xl:w-[42.5px] text-center">
                                                    ${standing.loss ?? ""}
                                                </div>
                                                <div class="w-[33px] xl:w-[42.5px] text-center">
                                                    ${standing.goals_for ?? ""}
                                                </div>
                                                <div class="w-[29px] xl:w-[42.5px] text-center">
                                                    ${
                                                        standing.goals_against ??
                                                        ""
                                                    }
                                                </div> 
                                                <div class="w-[29px] xl:w-[42.5px] text-center">
                                                    ${standing.goals_diff ?? ""}
                                                </div>
                                                <div class="w-[30px] xl:w-[42.5px] text-center">
                                                    ${standing.points ?? ""}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                `
                                    )
                                    .join("")}
                            `
                                    : ""
                            }
                        </div>
                    `
                        )
                        .join("")}

                    ${
                        completedOutCome(ranking.standings.groups).length > 0
                            ? `
                        <div class="flex flex-col gap-2 px-3 pt-4 xl:p-4 pb-6 max-xl:bg-neutral-790 text-match-primary-content text-sm">
                            <div class="font-medium">Ghi chú</div>
                            
                            ${completedOutCome(ranking.standings.groups)
                                .map(
                                    (outcome) => `
                                <div class="flex items-center gap-2 ">
                                    <div class="size-4 rounded" style="background-color: ${window.convertOutcome(
                                        outcome,
                                        true
                                    )};"></div>
                                    <div class="leading-4">${window.convertNameLeague(
                                        outcome
                                    )}</div>
                                </div>
                            `
                                )
                                .join("")}
                        </div>
                    `
                            : ""
                    }
                `
                        : ""
                }
            </div>
        `
            )
            .join("");
    }

    if (isSingleSeason) {
        return `
            <div
                data-season-id="${rankings.season.id}"
                class="js-toggle-season-btn flex items-center justify-between bg-match-desktop px-4 h-10 xl:p-2 border-b border-neutral-150 cursor-pointer "
            >
                <div class="flex items-center gap-2">
                    <img src="${
                        rankings.season.logo
                    }" alt="icon-season" class="w-[24px] aspect-square">
                    <div class="text-sm text-match-primary-content font-medium">${
                        rankings.season.name
                    }</div>
                </div>
            </div>

            <div 
                data-season-id="${rankings.season.id}"
                class="js-season-content active"
            >
                ${
                    rankings.groups.length > 0
                        ? `
                    ${rankings.groups
                        .map(
                            (group) => `
                        <div class="">
                            ${
                                group.id !== 0
                                    ? `
                                <div class="flex items-center h-9 text-sm  font-medium border-b px-4 border-neutral-150 text-match-primary-content bg-match-desktop max-xl:pl-3">
                                    Bảng ${group.group_name} 
                                </div>
                            `
                                    : ""
                            }

                            ${
                                group.standings.length > 0
                                    ? `
                                <div class="flex items-center justify-between px-4 xl:pr-5 bg-match-desktop h-8 text-xs text-match-secondary">
                                    <div class="flex items-center">
                                        <div class="w-[26px] text-center">TT</div>
                                        <div class="w-[76px] xl:w-[120px] ml-[10px] xl:ml-[32px] text-center">Đội</div>
                                    </div>
                                    <div class="flex items-center justify-between xl:gap-1">
                                        <div class="w-[29px] xl:w-[42.5px] text-center">Trận</div>
                                        <div class="w-[29px] xl:w-[42.5px] text-center">
                                            <span class="xl:hidden">Th</span>
                                            <span class="max-xl:hidden">Thắng</span>
                                        </div>
                                        <div class="w-[29px] xl:w-[42.5px] text-center">
                                            <span class="xl:hidden">H</span>
                                            <span class="max-xl:hidden">Hoà</span>
                                        </div>
                                        <div class="w-[28px] xl:w-[42.5px] text-center">Thua</div>
                                        <div class="w-[33px] xl:w-[42.5px] text-center">BT</div>
                                        <div class="w-[29px] xl:w-[42.5px] text-center">SBT</div> 
                                        <div class="w-[29px] xl:w-[42.5px] text-center">+/-</div>
                                        <div class="w-[30px] xl:w-[42.5px] text-center">Điểm</div>
                                    </div>
                                </div>

                                ${group.standings
                                    .map(
                                        (standing, index) => `
                                    <div class="flex w-full">
                                        <div class="flex flex-1 items-center justify-between px-4 h-12 text-sm text-match-primary-content leading-4 border-b border-neutral-150">
                                            <div class="flex items-center">
                                                <div class="w-[26px] text-center relative">
                                                    ${index + 1}
                                                    <div class="w-6 h-1.5 absolute bottom-[-16px] rounded-t left-0"
                                                        ${
                                                            standing.current_outcome
                                                                ? `style="background-color: ${window.convertOutcome(
                                                                      standing.current_outcome
                                                                  )};"`
                                                                : ""
                                                        }
                                                    ></div>
                                                </div>
                                                <div class="flex items-center gap-1 w-[76px] xl:w-[140px] ml-[10px] xl:ml-[32px]">
                                                    <img src="${
                                                        standing.competitor.logo
                                                    }" alt="logo-competitor" class="w-[20px] h-[20px] bg-neutral rounded-[2px]">
                                                    <div class="truncate line-clamp-1" title="${
                                                        standing.competitor.name
                                                    }">
                                                        ${
                                                            standing.competitor
                                                                .name
                                                        }
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="flex items-center justify-between xl:gap-1">
                                                <div class="w-[29px] xl:w-[42.5px] text-center">
                                                    ${standing.played ?? ""}
                                                </div>
                                                <div class="w-[29px] xl:w-[42.5px] text-center">
                                                    ${standing.win ?? ""}
                                                </div>
                                                <div class="w-[29px] xl:w-[42.5px] text-center">
                                                    ${standing.draw ?? ""}
                                                </div>
                                                <div class="w-[28px] xl:w-[42.5px] text-center">
                                                    ${standing.loss ?? ""}
                                                </div>
                                                <div class="w-[33px] xl:w-[42.5px] text-center">
                                                    ${standing.goals_for ?? ""}
                                                </div>
                                                <div class="w-[29px] xl:w-[42.5px] text-center">
                                                    ${
                                                        standing.goals_against ??
                                                        ""
                                                    }
                                                </div> 
                                                <div class="w-[29px] xl:w-[42.5px] text-center">
                                                    ${standing.goals_diff ?? ""}
                                                </div>
                                                <div class="w-[30px] xl:w-[42.5px] text-center">
                                                    ${standing.points ?? ""}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                `
                                    )
                                    .join("")}
                            `
                                    : ""
                            }
                        </div>
                    `
                        )
                        .join("")}

                    ${
                        completedOutCome(rankings.groups).length > 0
                            ? `
                        <div class="flex flex-col gap-[6px] px-3 pt-4 xl:p-4 pb-6 text-sm text-match-primary-content">
                            <div class="font-medium">Ghi chú</div>
                            
                            ${completedOutCome(rankings.groups)
                                .map(
                                    (outcome) => `
                                <div class="flex items-center gap-2">
                                    <div class="size-4 rounded" style="background-color: ${window.convertOutcome(
                                        outcome,
                                        true
                                    )};"></div>
                                    <div class="">${window.convertNameLeague(
                                        outcome
                                    )}</div>
                                </div>
                            `
                                )
                                .join("")}
                        </div>
                    `
                            : ""
                    }
                `
                        : ""
                }
            </div>
        `;
    }
};

// ========== Results content ==========
window.resultsContentRendering = (season,index) => `
    <div class="mb-1">
        <div
            data-season-id="${season.season_info.id}"
            class="js-toggle-season-btn flex items-center justify-between bg-match-secondary-surface px-4 h-10 border-b border-neutral-150 cursor-pointer ${index == 0 ? 'border-t' : ''}"
        >
            <div class="flex items-center gap-2">
                <img src="${
                    season.season_info.logo
                }" alt="icon-season" class="w-[24px] aspect-square">
                <div class="text-sm text-match-primary-content font-medium">${
                    season.season_info.name
                }</div>
            </div>
            <div class="flex items-center gap-2.5">
                <div class="hidden size-6 text-sm font-medium text-match-secondary items-center justify-center js-toggle-season-count rounded bg-match-primary">0</div>
                <img 
                    src="/modules/matchschedule/images/icons/arrow-up.svg"
                    alt="icon-arrow-right"
                    class="js-toggle-season-icon w-[24px] h-[24px] z-[1]"
                />
            </div>
        </div>

        <div 
            data-season-id="${season.season_info.id}"
            class="js-season-content active"
        >
            ${season.events
                .map(
                    (match, index) => `
                <div class="js-match-item gap-2 px-4 py-4 max-xl:justify-between max-xl:pr-[10px] ${
                    index < season.events.length - 1
                        ? "border-b border-neutral-650"
                        : ""
                }">
                    <div class="flex items-center gap-4 h-[44px]">
                        <div class="flex text-sm items-center">
                            <div class="text-match-secondary">
                                ${formatDateTime(
                                    match.sport_event.start_time,
                                    "DD/MM"
                                )}
                            </div>
                            <span class="mx-1 text-match-secondary"> - </span>
                            <div class="font-medium text-match-primary-content">
                                ${formatDateTime(
                                    match.sport_event.start_time,
                                    "HH:mm"
                                )}
                            </div>
                        </div>
                        <div 
                            class="flex items-center justify-center text-xs font-medium leading-4 px-2 py-[2px] max-xl:py-[1px] rounded-full"
                            style="border: 1px solid ${
                                window.getMatchStatus(
                                    match.sport_event_status.status
                                ).border
                            }; color: ${
                                window.getMatchStatus(
                                    match.sport_event_status.status
                                ).color
                            };"
                        >
                            ${
                                window.getMatchStatus(
                                    match.sport_event_status.status
                                ).label
                            }
                        </div>
                    </div>
                    <div class="flex items-center gap-2">
                        <div class="flex flex-col gap-2">
                            <div class="flex items-center gap-[10px] xl:gap-[20px] justify-between">
                                <div class="flex items-center gap-2 xl:w-[300px] overflow-hidden">
                                    <img 
                                        src="${
                                            match.sport_event.competitors[0].logo
                                        }" 
                                        alt="icon-competitor" 
                                        class="min-w-[20px] min-h-[20px] max-w-[20px] max-h-[20px]"
                                        onerror="this.onerror=null; this.src='/modules/matchschedule/images/tournaments/default.avif';"
                                    >
                                    <div class="!text-sm text-match-primary-content flex-1">
                                        <div class="truncate line-clamp-1 w-[108px] max-xl:text-xs xl:w-full xl:max-w-[240px]" title="${
                                            match.sport_event.competitors[0].name
                                        }">
                                            ${match.sport_event.competitors[0].name}
                                        </div>
                                    </div>
                                </div>
                                <div class="flex items-center gap-1.5 mt-[2px]">
                                    <div class="text-xs xl:w-[100px] max-[389px]:w-[60px] max-xl:text-center max-xl:w-[90px] text-match-primary-content font-medium">
                                        ${match.sport_event_status.home_score ?? 0}
                                    </div>
                                    <div class="flex items-center text-match-primary-content justify-between px-3 w-[88px] xl:w-[100px]">
                                        <div class="flex items-center gap-1.5">
                                            <div class="w-3 h-4 bg-match-alert-status-warning rounded-[2px]"></div>
                                            <div class="text-xs/[18px]">
                                                ${
                                                    match.statistics.totals
                                                        .competitors[0]
                                                        .statistics
                                                        .yellow_cards ?? 0
                                                }
                                            </div>
                                        </div>
                                        <div class="flex items-center gap-1.5">
                                            <div class="w-3 h-4 bg-match-alert-status-error rounded-[2px]"></div>
                                            <div class="text-xs/[18px]">
                                                ${
                                                    match.statistics.totals
                                                        .competitors[0]
                                                        .statistics.red_cards ??
                                                    0
                                                }
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center gap-[10px] xl:gap-[20px] justify-between">
                                <div class="flex items-center gap-2 xl:w-[300px] overflow-hidden">
                                    <img 
                                        src="${
                                            match.sport_event.competitors[1].logo
                                        }" 
                                        alt="icon-competitor" 
                                        class="min-w-[20px] min-h-[20px] max-w-[20px] max-h-[20px]"
                                        onerror="this.onerror=null; this.src='/modules/matchschedule/images/tournaments/default.avif';"
                                    >
                                    <div class="text-sm text-match-primary-content  flex-1">
                                        <div class="truncate line-clamp-1 w-[108px] max-xl:text-xs xl:w-full xl:max-w-[240px]" title="${
                                            match.sport_event.competitors[1].name
                                        }">
                                            ${match.sport_event.competitors[1].name}
                                        </div>
                                    </div>
                                </div>
                                <div class="flex items-center gap-1.5 mt-[2px]">
                                    <div class="text-xs xl:w-[100px] max-[389px]:w-[60px] max-xl:w-[90px] max-xl:text-center text-match-primary-content font-medium">
                                        ${match.sport_event_status.away_score ?? 0}
                                    </div>
                                    <div class="flex items-center text-match-primary-content justify-between px-3 w-[88px] xl:w-[100px]">
                                        <div class="flex items-center gap-1.5">
                                            <div class="w-3 h-4 bg-match-alert-status-warning rounded-[2px]"></div>
                                            <div class="text-xs/[18px]">
                                                ${
                                                    match.statistics.totals
                                                        .competitors[1]
                                                        .statistics
                                                        .yellow_cards ?? 0
                                                }
                                            </div>
                                        </div>
                                        <div class="flex items-center gap-1.5">
                                            <div class="w-3 h-4 bg-match-alert-status-error rounded-[2px]"></div>
                                            <div class="text-xs/[18px]">
                                                ${
                                                    match.statistics.totals
                                                        .competitors[1]
                                                        .statistics.red_cards ??
                                                    0
                                                }
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `
                )
                .join("")}
        </div>
    </div>
`;
