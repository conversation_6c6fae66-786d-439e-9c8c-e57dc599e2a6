
const SITE_URLS = {
    MATCH_SCHEDULE: '/bong-da/lich-thi-dau',
    ODDS: '/bong-da/ty-le-keo-bong-da',
    RANKINGS: '/bong-da/bang-xep-hang-bong-da',
    RESULTS: '/bong-da/ket-qua-bong-da',
}

const SITE_NAMES = {
    MATCH_SCHEDULE: 'match-schedule',
    ODDS: 'odds',
    RANKINGS: 'rankings',
    RESULTS: 'results',
}

const K_SPORT_URL = '/ca-do-bong-da/ksports';
const K_SPORT_API = '/tp/ksportUrl?loginPath=?type=modal-login&registerPath=?type=modal-register'

document.addEventListener('DOMContentLoaded', () => {
    const getCurrentSite = () => {
        const url = window.location.pathname;
        
        switch (url) {
            case SITE_URLS.MATCH_SCHEDULE:
                return SITE_NAMES.MATCH_SCHEDULE;
            case SITE_URLS.ODDS:
                return SITE_NAMES.ODDS;
            case SITE_URLS.RANKINGS:
                return SITE_NAMES.RANKINGS;
            case SITE_URLS.RESULTS:
                return SITE_NAMES.RESULTS;
            default:
                return SITE_NAMES.MATCH_SCHEDULE;
        }
    }

    // const getCurrentParams = () => {
    //     const oldParams = window.history.state ? Object.fromEntries(
    //         Object.entries(window.history.state).map(([key, value]) => {
    //             if (typeof value === 'string' && value.includes('=')) {
    //                 const [paramKey, paramValue] = value.split('=');
    //                 return [paramKey, paramValue];
    //             }
    //             return [key, value];
    //         })
    //     ) : {};
    //     return oldParams;
    // };

    const scrollIntoViewTopMenu = () => {
        const topMenuContainer = document.querySelector('.js-top-menu-container');
        const topMenu = document.querySelector('.js-active-top-menu');
        if (topMenuContainer && topMenu) {
            topMenu.scrollIntoView({behavior: 'smooth', block: 'center', });
        }
    };

    scrollIntoViewTopMenu();

    const toggleUpdateUrl = (params) => {
        const newUrl = `${window.location.pathname}?${Object.entries(params).map(([key, value]) => `${key}=${value}`).join('&')}`;
        window.history.pushState({...params}, '', newUrl);
    };

    const toggleClickTournament = (seasons) => {
        const params = new URLSearchParams(window.location.search);
        const queryParamsObj = Object.fromEntries(params.entries());

        const currentSeasons = queryParamsObj?.seasons ?? 'all';

        if (seasons && seasons !== currentSeasons) {
            const oldParams = queryParamsObj ?? {};
            
            const newParams = {...oldParams, seasons : seasons}
            
            toggleUpdateUrl(newParams);
            fetchNewContentData(newParams);
            
            //set active tournament
            toggleActiveTournament(seasons);
        }
    }

    // ==================================================
    // ================== MATCH SCHEDULE ================
    // Search tournament
    $('.search-tournament-container').each(function() {
        const searchInput = $(this).find('.search-tournament-input');
        const searchRemove = $(this).find('.search-tournament-remove');
        const searchTournamentBtn = $(this).find('.search-tournament-btn');
        const tournamentListSearch = $(this).find('.tournament-list-search');

        const closeSearchTournamentMb = $(this).find('.close-search-tournament-mb');
        const tournamentListSearchOverlay = $(this).find('.tournament-list-search-overlay');
        const tournamentListBlur = $(this).find('.tournament-list-blur');
        const tournamentListCloseDropdown = $(this).find('.tournament-list-close-dropdown');

        searchInput.keydown(function(event){
            if(event.keyCode == 13) {
              event.preventDefault();
              return false;
            }
        });

        searchRemove.on('click', function () {
            $(this).addClass('invisible');
            searchInput.val('');
            setTimeout(() => {
                searchInput.focus();
                searchInput.trigger('input');
            }, 0);
        });

        searchInput.on('focus', function () {
            tournamentListSearch.addClass('flex');
            tournamentListSearch.removeClass('hidden');

            if (screen.width >= 1200) {
                const $active = $('.tournaments-container .tournament-item-search.active');

                if ($active.length) {
                    $active.each(function (index, item) {
                        item.scrollIntoView({ behavior: 'auto', block: 'nearest' });
                    })
                }
            } else {
                window.scrollTo(0, 0);
            }
        });

        searchInput.on('blur', function () {
            if (screen.width < 1200) {
                window.scrollTo(0, 0);
            }
        });

        // Track if we're clicking inside the search container
        let isClickingInsideSearch = false;
        
        // Listen for mousedown events on the search container
        $(document).on('mousedown', '.search-tournament-container', function() {
            isClickingInsideSearch = true;
        });
        
        // Listen for mousedown events outside the search container
        $(document).on('mousedown', function(e) {
            if (!$(e.target).closest('.search-tournament-container').length) {
                isClickingInsideSearch = false;
            }
        });
        
        searchInput.on('blur', function (event) {
            if (!isClickingInsideSearch) {
                setTimeout(() => {
                    tournamentListSearch.removeClass('flex');
                    tournamentListSearch.addClass('hidden');
                }, 0);
            }
        });

        const handleSearchTournament = ({value, isMb = false}) => {
            const listItem = $('.tournament-item-search');
            const listItemEmpty = $('.tournaments-list-empty');

            if (value.length > 0 && globalMatchScheduleLeagues?.length > 0) {
                let valid = [];

                listItem.each(function(index,item) {
                    const { name } = $(item).data();

                    if (!name || !name.toLowerCase().includes(value.toLowerCase())) {
                        $(item).addClass('hidden').removeClass('flex');
                    } else {
                        $(item).addClass('flex').removeClass('hidden');
                        valid.push(index);
                    }
                })

                if (valid.length === 0) {
                    listItemEmpty.addClass('flex').removeClass('hidden');
                } else {
                    listItemEmpty.addClass('hidden').removeClass('flex');
                }

                searchRemove.removeClass('invisible');
            } else {
                listItemEmpty.addClass('hidden').removeClass('flex');
                listItem.addClass('flex').removeClass('hidden');
                searchRemove.addClass('invisible');
            }
        }

        window.handleSearchTournament = handleSearchTournament;

        searchInput.on('input change', function (event) {
            const value = event.target.value;
            handleSearchTournament({value});
        });

        const handleCloseDropdown = () => {
            $('.js-search-tournament-arrow').removeClass('rotate-180');
            $('.tournament-list-search').removeClass('flex');
            $('.tournament-list-search').addClass('hidden');
            tournamentListBlur.addClass('hidden').removeClass('block');
            $('body').removeClass('overflow-hidden');
        }

        searchTournamentBtn.on('click', function () {
            if ($('.tournament-list-search').hasClass('flex')) {
                handleCloseDropdown();
            } else {
                tournamentListSearchOverlay.removeClass('hidden');
                $('.js-search-tournament-arrow').addClass('rotate-180')
                tournamentListSearch.addClass('flex');
                tournamentListSearch.removeClass('hidden');
                tournamentListBlur.addClass('block').removeClass('hidden');

                if (screen.width < 1200) {
                    $('body').addClass('overflow-hidden');
                }

                const $active = $('.tournaments-container .tournament-item-search.active');

                if ($active.length) {
                    $active.each(function (index, item) {
                        item.scrollIntoView({ behavior: 'auto', block: 'nearest' });
                    })
                }
            }
        });

        tournamentListBlur.on('click', function () {
            handleCloseDropdown();
        });

        tournamentListCloseDropdown.on('click', function () {
            handleCloseDropdown();
        });

        closeSearchTournamentMb.on('click', function () {
            tournamentListSearchOverlay.addClass('hidden');
            tournamentListSearch.removeClass('flex');
            tournamentListSearch.addClass('hidden');
        });
    });

    // ========== Tournament list ==========
    const toggleActiveTournament = (seasons) => {
        const tournamentItems = document.querySelectorAll('.js-tournament-item');
        tournamentItems.forEach(item => {
            item.classList.remove('active');
        });

        const activeTournament = document.querySelectorAll(`.js-tournament-item[data-seasons="${seasons}"]`);
        activeTournament.forEach(item => {
            item.classList.add('active');
        });
    };

    const toggleActiveTournamentName = (seasons) => {
        const params = new URLSearchParams(window.location.search);
        const queryParamsObj = Object.fromEntries(params.entries());
        const currentSeasons = queryParamsObj?.seasons ?? 'all';

        const searchTournamentName = $('.search-tournament-container').find('.js-search-tournament-name');

        if (currentSeasons === 'all') {
            searchTournamentName.text('Chọn Giải Đấu');
        } else {
            const seasonName = window.globalMatchScheduleLeagues.find(league => league.id === currentSeasons)?.name;
            searchTournamentName.text(seasonName);
        }
    };

    toggleActiveTournamentName();

    // Toggle click tournament
    $(document).on('click', '.js-tournament-item', function(e) {
        e.preventDefault();
        const seasons = $(this).data('seasons');

        toggleClickTournament(seasons);
        toggleActiveTournamentName(seasons);

        const tournamentListSearch = $(this).closest('.search-tournament-container').find('.tournament-list-search');
        const tournamentListSearchOverlay = $(this).closest('.search-tournament-container').find('.tournament-list-search-overlay');

        tournamentListSearchOverlay.addClass('hidden');
        tournamentListSearch.addClass('hidden');
        tournamentListSearch.removeClass('flex');
        $('.tournament-list-blur').addClass('hidden').removeClass('block');
        $('.js-search-tournament-arrow').removeClass('rotate-180');

        const league = globalMatchScheduleLeagues.find((item)=>item.id == seasons);

        if (screen.width >= 1200) {
            $('.search-tournament-input').val(league.name);
            handleSearchTournament({value:league.name});

            setTimeout(()=>{
                $('.search-tournament-input').trigger('input');
                $('.close-search-tournament-mb').trigger('click');
            },0);
        } else {
            $('body').removeClass('overflow-hidden');
        }
    });

    // ========== Date filter ==========
    const toggleActiveDateFilter = (filterLabel) => {
        $('.js-date-filter-item').removeClass('active');
        $(`.js-date-filter-item[data-filter-label="${filterLabel}"]`).addClass('active');
    }

    const dateFilter = document.querySelectorAll('.js-date-filter-item');
    dateFilter.forEach(item => {
        item.addEventListener('click', function (e) {
            e.preventDefault();
            const params = new URLSearchParams(window.location.search);
            const filter = $(this).data('filter');
            const filterLabel = $(this).data('filter-label');
            const queryParamsObj = Object.fromEntries(params.entries());

            const oldFilterLabel = queryParamsObj?.filterLabel ?? 'all';
            const oldParams =  {...queryParamsObj};

            if (oldFilterLabel !== filterLabel) {
                let date = new Date().toISOString().split('T')[0];
                if (filter && filterLabel == 'tomorrow') {
                    date = new Date(new Date(date).getTime() + 24 * 60 * 60 * 1000).toISOString().split('T')[0];
                }
                
                if (filter && filterLabel == 'yesterday') {
                    date = new Date(new Date(date).getTime() - 24 * 60 * 60 * 1000).toISOString().split('T')[0];
                }
                
                const newParams = {...oldParams, filter, filterLabel, ...(date ? {date} : {})};
                toggleActiveDateFilter(filterLabel);
                toggleUpdateUrl(newParams);
                fetchNewContentData(newParams);
            }
        });
    });

    // ========== Match schedule content ==========
    // Collapse
    const initCollapseSeasonClick = () => {
        // Set initial state for all season content
        const allContents = document.querySelectorAll('.js-season-content');
        allContents.forEach(content => {
            content.style.maxHeight = content.scrollHeight + "px";
            content.classList.add('active');
        });
        
        // Toggle click season
        const toggleButtons = document.querySelectorAll('.js-toggle-season-btn');
        toggleButtons.forEach(button => {
            button.addEventListener('click', function() {
                const seasonId = this.dataset.seasonId;
                const content = document.querySelector(`.js-season-content[data-season-id="${seasonId}"]`);
                const header = $(`.js-toggle-season-btn[data-season-id="${seasonId}"]`)
                const icon = this.querySelector('.js-toggle-season-icon');
                
                // Toggle content visibility with slide effect
                if (content.classList.contains('active')) {
                    content.style.maxHeight = null;
                    icon.style.transform = 'rotate(180deg)';
                    content.classList.remove('active');
                    const a = $(content).find('.js-match-item').length;
                    const b = $(header).find('.js-toggle-season-count');
                    b.text(a);
                    b.removeClass('hidden').addClass('flex');
                    // this.classList.remove('border-b')
                } else {
                    content.style.maxHeight = content.scrollHeight + "px";
                    icon.style.transform = 'rotate(0deg)';
                    content.classList.add('active');
                    $(header).find('.js-toggle-season-count').addClass('hidden').removeClass('flex');
                    this.classList.add('border-b');
                }
            });
        });
    };

    initCollapseSeasonClick();

    // Fetch new content
    const fetchNewContentData = async (params) => {
        const currentSite = getCurrentSite();

        const contentLoading = document.querySelector('.js-content-loading');
        const contentContainer = document.querySelector('.js-content-container');
        contentLoading.classList.remove('hidden');
        contentContainer.classList.add('hidden');

        let finalParams = {};
        let response = {};

        if (currentSite === SITE_NAMES.MATCH_SCHEDULE || currentSite === SITE_NAMES.ODDS) {
            finalParams = {
                limit: params.limit || 100,
                filter: params.filter || 'all',
                seasons: params.seasons || 'all',
                date: params.date || new Date().toISOString().split('T')[0],
                odds: params.odds || currentSite === SITE_NAMES.ODDS ? 'true' : 'false',
                // timezone: params.timezone || -(new Date().getTimezoneOffset() / 60)
                timezone: params.timezone || -420,
            };

            if (!params.seasons || params.seasons == 'all') {
                finalParams.num_seasons = params.num_seasons || 20;
            }

            response = await fetchData('/api-promotion/v1/livescore/seasons/summaries', finalParams, {useProxy:false});
        } else if (currentSite === SITE_NAMES.RESULTS) {
            finalParams = {
                filter: params.filter || 'all',
                match: params.match || 'result',
                odds: params.odds || 'false',
                date: params.date || new Date().toISOString().split('T')[0],
                timezone: params.timezone || -420,
            };

            if (params.seasons && params.seasons !== 'all') {
                finalParams.seasons = params.seasons;
            }

            if (!params.seasons || params.seasons == 'all') {
                finalParams.num_seasons = params.num_seasons || 20;
            }

            response = await fetchData('/api-promotion/v1/livescore/seasons/summaries', finalParams, {useProxy:false});
         } else if (currentSite === SITE_NAMES.RANKINGS) {
            if (!params.seasons || params.seasons ==='all') {
                finalParams.round = params.round || 0;
                finalParams.offset = params.offset || 0;
                finalParams.limit = params.limit || 100;
                finalParams.num_seasons = params.num_seasons || 20;
                finalParams.timezone = params.timezone || -420;
            } else {
                finalParams.season = params.seasons;
            }

            response = await fetchData('/api-promotion/v1/livescore/standings', finalParams, {useProxy:false});
        }

        if (response.status == 'OK') {
            const contentList = response.data;
            toggleRenderNewContent(contentList);
        }

        contentLoading.classList.add('hidden');
        contentContainer.classList.remove('hidden');
    };

    // ========== Render content list ==========
    const toggleRenderNewContent = (contentList) => {
        const currentSite = getCurrentSite();

        const listContainer = document.querySelector(`.js-${currentSite}-list-container`);
        listContainer.innerHTML = '';

        if (!contentList || contentList.length === 0) {
            listContainer.innerHTML = window.emptyContentRendering();
        } else {
            if (currentSite === SITE_NAMES.MATCH_SCHEDULE) {
                const newContent = contentList.map((season,index) => 
                    window.matchScheduleContentRendering(season,index)
                ).join('');
                listContainer.innerHTML = newContent;
            }

            if (currentSite === SITE_NAMES.ODDS) {
                const newContent = contentList.map((season,index) => 
                    window.oddsContentRendering(season,index)
                ).join('');
                listContainer.innerHTML = newContent;
            }

            if (currentSite === SITE_NAMES.RESULTS) {
                const newContent = contentList.map((season,index) => 
                    window.resultsContentRendering(season,index)
                ).join('');
                listContainer.innerHTML = newContent;
            }

            if (currentSite === SITE_NAMES.RANKINGS) {
                const newContent = window.rankingsContentRendering(contentList)
                listContainer.innerHTML = newContent;
            }

            setTimeout(() => {
                initCollapseSeasonClick();
                initOddsClickBetNowBtn();
            }, 200);
        }
    };

    // ==================================================
    // ==================== ODDS ========================
    const initOddsClickBetNowBtn = () => {
        $('.js-odds-bet-now-btn').on('click', async function () {
            $(this).attr('disabled', true);
            $(this).addClass('cursor-progress opacity-50');
    
            const leagueId = $(this).data('league-id');
            const matchId = $(this).data('match-id');
    
            try {
                await openSport({
                    link: `${K_SPORT_URL}?leagueId=${leagueId}&matchId=${matchId}`,
                    apiUrl: K_SPORT_API,
                    loginRequired: false,
                    params: {
                        leagueId: leagueId,
                        matchId: matchId,
                    }
                })
            } catch (error) {
                console.error(error);
            }
    
            $(this).attr('disabled', false);
            $(this).removeClass('cursor-progress opacity-50');
        });
    };

    initOddsClickBetNowBtn();
});
