<?php

namespace Modules\MatchSchedule\Http\Controllers;

use App\Http\Controllers\Controller;
use Modules\MatchSchedule\Services\MatchScheduleService;
use Illuminate\Http\Request;

class MatchScheduleController extends Controller
{
    protected $matchScheduleService;

    public function __construct(MatchScheduleService $matchScheduleService)
    {
        $this->matchScheduleService = $matchScheduleService;
    }

    public function index()
    {
        // Get timezone offset
        // $timezone = ((new \DateTime())->getOffset() / 60);

        $date = request()->get('date') ?? now()->format('Y-m-d');
        $seasons = request()->get('seasons') ?? 'all';
        $filter = request()->get('filter') ?? 'all';
        $odds = request()->get('odds') ?? 'false';
        $limit = request()->get('limit') ?? 100;
        $num_seasons = request()->get('num_seasons') ?? config('matchschedule.competitions_limit');

        $params = [
            'date' => $date,
            'filter' => $filter,
            'odds' => $odds,
            'seasons' => $seasons,
            'timezone' => -420,
            'limit' => $limit,
        ];

        if ($seasons === 'all') {
            $params['num_seasons'] = $num_seasons;
        }

        $matchSchedule = $this->matchScheduleService->getMatchSchedule($params);

        return $this->renderCommonView('pages.match-schedule', [
            'matchSchedule' => $matchSchedule
        ]);
    }

    public function odds()
    {
        // $timezone = ((new \DateTime())->getOffset() / 60);

        $date = request()->get('date') ?? now()->format('Y-m-d');
        $seasons = request()->get('seasons') ?? 'all';
        $filter = request()->get('filter') ?? 'all';
        $odds = request()->get('odds') ?? 'true';
        $limit = request()->get('limit') ?? 100;
        $num_seasons = request()->get('num_seasons') ?? config('matchschedule.competitions_limit');

        $params = [
            'date' => $date,
            'filter' => $filter,
            'odds' => $odds,
            'seasons' => $seasons,
            'timezone' => -420,
            'limit' => $limit,
        ];

        if ($seasons === 'all') {
            $params['num_seasons'] = $num_seasons;
        }

        $odds = $this->matchScheduleService->getMatchSchedule($params);

        return $this->renderCommonView('pages.odds', ['odds' => $odds]);
    }

    public function rankings()
    {
        // $timezone = ((new \DateTime())->getOffset() / 60);

        $round = request()->get('round') ?? 0;
        $offset = request()->get('offset') ?? 0;
        $limit = request()->get('limit') ?? 100;
        $season = request()->get('seasons') ?? null;
        $num_seasons = request()->get('num_seasons') ?? config('matchschedule.competitions_limit');

        $params = [
            'timezone' => -420,
        ];

        if (!isset($season) || $season =='all') {
            $params['round'] = $round;
            $params['offset'] = $offset;
            $params['limit'] = $limit;
            $params['num_seasons'] = $num_seasons;
        } else {
            $params['season'] = $season;
        }
        
        $rankings = $this->matchScheduleService->getRankings($params);

        return $this->renderCommonView('pages.rankings', ['rankings' => $rankings]);
    }

    public function results()
    {
        // $timezone = ((new \DateTime())->getOffset() / 60);

        $date = request()->get('date') ?? now()->format('Y-m-d');
        $seasons = request()->get('seasons') ?? null;
        $filter = request()->get('filter') ?? 'all';
        $odds = request()->get('odds') ?? 'false';
        $match = request()->get('match') ?? 'result';
        $num_seasons = request()->get('num_seasons') ?? config('matchschedule.competitions_limit');

        $params = [
            'date' => $date,
            'filter' => $filter,
            'odds' => $odds,
            'match' => $match,
            'timezone' => -420,
        ];

        if (isset($seasons) && $seasons !== 'all') {
            $params['seasons'] = $seasons;
        }

        if (!isset($seasons) || $seasons === 'all') {
            $params['num_seasons'] = $num_seasons;
        }

        $results = $this->matchScheduleService->getMatchSchedule($params);

        return $this->renderCommonView('pages.results', ['results' => $results]);
    }

    private function renderCommonView(string $view, array $additionalData = [])
    {
        $leagues = $this->matchScheduleService->getLeagues();
        $news = $this->matchScheduleService->getNewsByCategory();

        $data = array_merge([
            'leagues' => $leagues,
            'news' => $news
        ], $additionalData);

        return view("match-schedule::$view", $data);
    }
}
