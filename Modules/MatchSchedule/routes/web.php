<?php

use Illuminate\Support\Facades\Route;
use Modules\MatchSchedule\Http\Controllers\MatchScheduleController;

Route::group(['prefix' => 'bong-da'], function () {
    Route::get('/lich-thi-dau', [MatchScheduleController::class, 'index'])->name('match-schedule.match-schedule');
    Route::get('/ty-le-keo-bong-da', [MatchScheduleController::class, 'odds'])->name('match-schedule.odds');
    Route::get('/bang-xep-hang-bong-da', [MatchScheduleController::class, 'rankings'])->name('match-schedule.rankings');
    Route::get('/ket-qua-bong-da', [MatchScheduleController::class, 'results'])->name('match-schedule.results');
});