# Image Optimization Guide

## Overview
This guide explains the image optimization system implemented to improve website performance and reduce image delivery size by up to 906 KiB as requested.

## Features Implemented

### 1. Modern Image Format Support
- **AVIF**: Best compression (up to 50% smaller than JPEG)
- **WebP**: Good compression with wide browser support
- **Fallback**: Original format for older browsers

### 2. Automatic Format Detection
The system automatically serves the best supported format:
```html
<picture>
    <source srcset="image.avif" type="image/avif">
    <source srcset="image.webp" type="image/webp">
    <img src="image.jpg" alt="fallback">
</picture>
```

### 3. Lazy Loading with Intersection Observer
- Images load only when they enter the viewport
- Reduces initial page load time
- Includes skeleton loading animation

### 4. Cache Optimization
- Long-term caching (1 year) for static assets
- Immutable cache headers
- Gzip and Brotli compression

## Usage

### Using the Optimized Image Component
```blade
<x-kit.optimized-img 
    src="/asset/images/example.jpg"
    alt="Example image"
    class="w-full h-auto"
    fallback="/asset/images/default.avif"
    lazy="true"
    width="800"
    height="600"
/>
```

### Using Helper Functions
```php
// Generate picture element
echo optimized_image('/asset/images/example.jpg', 'Alt text', 'css-class');

// Get modern format sources
$sources = get_modern_image_sources('/asset/images/example.jpg');

// Get image URL with cache busting
$url = image_url_with_cache('/asset/images/example.jpg');
```

### Using the Updated img Component
```blade
<x-kit.img 
    src="/asset/images/example.jpg"
    alt="Example image"
    className="w-full h-auto"
    errorSrc="/asset/images/default.avif"
    useModernFormats="true"
/>
```

## Commands

### Convert Images to Modern Formats
```bash
# Convert all images
php artisan images:optimize

# Convert specific directory
php artisan images:optimize subfolder

# Custom quality and formats
php artisan images:optimize --quality=70 --formats=avif,webp

# Force re-conversion
php artisan images:optimize --force
```

### Run Complete Optimization
```bash
chmod +x scripts/optimize-performance.sh
./scripts/optimize-performance.sh
```

## Configuration

### Nginx Configuration
The nginx config includes:
- Gzip compression for all text assets
- Long-term caching for images (1 year)
- Automatic modern format serving
- Cache headers optimization

### Vite Configuration
- Asset file naming with hashes for cache busting
- Compression plugins (Gzip + Brotli)
- Chunk splitting for better caching
- Minification and tree shaking

## Performance Benefits

### Expected Improvements
1. **Image Size Reduction**: 40-60% smaller files with AVIF/WebP
2. **Faster Loading**: Lazy loading reduces initial page weight
3. **Better Caching**: Long-term cache reduces repeat visit load times
4. **Core Web Vitals**: Improved LCP, CLS, and FID scores

### Monitoring
Monitor these metrics:
- **LCP (Largest Contentful Paint)**: Should improve with faster image loading
- **CLS (Cumulative Layout Shift)**: Prevented with proper image dimensions
- **FID (First Input Delay)**: Better with optimized JavaScript loading

## Browser Support

### AVIF Support
- Chrome 85+
- Firefox 93+
- Safari 16+

### WebP Support
- Chrome 23+
- Firefox 65+
- Safari 14+
- Edge 18+

### Fallback
All browsers support the original format fallback.

## Best Practices

### 1. Image Preparation
- Use appropriate dimensions for your use case
- Optimize source images before conversion
- Consider responsive images for different screen sizes

### 2. Implementation
```blade
<!-- Good: Specify dimensions to prevent layout shift -->
<x-kit.optimized-img 
    src="/asset/images/hero.jpg"
    width="1200"
    height="600"
    alt="Hero image"
/>

<!-- Good: Use appropriate quality settings -->
php artisan images:optimize --quality=80 --formats=avif,webp
```

### 3. Testing
- Test on different devices and browsers
- Use Chrome DevTools to verify format serving
- Monitor Core Web Vitals in Google Search Console

## Troubleshooting

### Images Not Converting
1. Check Sharp installation: `npm list sharp`
2. Verify file permissions on image directories
3. Check Node.js version compatibility

### Modern Formats Not Serving
1. Verify nginx configuration is applied
2. Check if converted files exist
3. Test with browser developer tools

### Performance Not Improving
1. Verify cache headers are working
2. Check if images are actually lazy loading
3. Monitor network tab in developer tools

## File Structure
```
app/
├── Console/Commands/OptimizeImages.php
├── Helpers/
│   ├── ImageHelper.php
│   └── image_helpers.php
resources/
├── js/optimized-image.js
├── sass/components/_optimized-images.scss
└── views/components/kit/
    ├── img.blade.php
    └── optimized-img.blade.php
docker/nginx/conf.d/default.conf
scripts/optimize-performance.sh
```

## Additional Optimizations

### 1. CDN Integration
Consider using a CDN like Cloudflare for global image delivery.

### 2. Responsive Images
Implement srcset for different screen sizes:
```html
<img srcset="image-480.avif 480w, image-800.avif 800w" 
     sizes="(max-width: 480px) 480px, 800px">
```

### 3. Critical Images
Mark above-the-fold images as critical:
```blade
<x-kit.optimized-img 
    src="/asset/images/hero.jpg"
    lazy="false"
    class="critical-image"
/>
```
