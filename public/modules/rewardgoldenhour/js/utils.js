document.addEventListener("DOMContentLoaded", async () => {
    const policyButtons = $('.js-reward-policy-button');
    const termContent = $('.js-reward-policy-term');
    const termContentScroll = $('.js-reward-policy-term-scroll');
    const ruleContent = $('.js-reward-policy-rule');
    const rewardDepositNow = $('.js-reward-deposit-now');

    policyButtons.each(function () {
        $(this).on('click', function () {
            policyButtons.removeClass('active');
            $(this).addClass('active');

            const type = $(this).data('type');

            if (type === 'rule') {
                termContent.addClass('hidden').removeClass('flex');
                ruleContent.addClass('flex').removeClass('hidden');
                termContentScroll.scrollTop(0);
            } else {
                termContent.addClass('flex').removeClass('hidden');
                ruleContent.addClass('hidden').removeClass('flex');
                termContentScroll.scrollTop(0);
            }
        })
    })

    rewardDepositNow.on('click', function () {
        const cookies = getCookies();
        if (!cookies || !cookies.user) {
            openSignup();
            return;
        } else {
            window.location.href = '/account/deposit';
        }
    })
});
