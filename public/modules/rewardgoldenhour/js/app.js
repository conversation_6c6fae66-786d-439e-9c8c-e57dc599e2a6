// const checkRewardGoldenHour = async () => {
//     try {
//         const { status, data } = await fetchData('/lixi/newyear', {}, "", "", "");

//         if (status === 'OK') {
//             return data.check;
//         }
//         return false;
//     } catch (error) {
//         return false
//     }

// }
// const claimRewardGoldenHour = async () => {
//     $('body').addClass('pointer-events-none');
//     $('.reward-event-open-button').addClass('hidden').removeClass('block');
//     $('.reward-event-loading').addClass('block').removeClass('hidden');

//     try {
//         const res = await submitData('/lixi/newyear', {}, "", "", "");
//         if (res?.status === 'OK') {
//             openModal(window.rewardGoldenHourResultModal, true, 'reward-golden-hour-result-modal');

//             $('.js-reward-money').text(`${res?.amount_txt} VND`);
//             $('body').removeClass('pointer-events-none');
//         } else {
//             openModal(window.rewardGoldenHourExpireModal, true, 'reward-golden-hour-expire-modal');
//         }
//         $('body').removeClass('pointer-events-none');
//         $('.reward-event-open-button').addClass('block').removeClass('hidden');
//         $('.reward-event-loading').addClass('hidden').removeClass('block');
//     } catch (error) {
//         $('body').removeClass('pointer-events-none');
//         $('.reward-event-open-button').addClass('block').removeClass('hidden');
//         $('.reward-event-loading').addClass('hidden').removeClass('block');
//     }
// }

// document.addEventListener('DOMContentLoaded', async () => {
//     const check = await checkRewardGoldenHour();

//     const rewardGoldenHourGiftModal = window.rewardGoldenHourGiftModal ?? null;
    
//     if (check && rewardGoldenHourGiftModal) {
//         window.claimRewardGoldenHour = claimRewardGoldenHour;
//         openModal(rewardGoldenHourGiftModal, true, 'reward-golden-hour-gift-modal');
//     }
// });
