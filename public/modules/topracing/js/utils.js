let countdownRankTimer = null;

function formatDateTimeToDDMMYYYY(input,type) {
    if (type === 'with-hours') {
        const [datePart, timePart] = input.split(' ');
        const [year, month, day] = datePart.split('-');

        return `${day}/${month}/${year} ${timePart}`;
    } else {
        const [day, month, year] = input.split('/');

        return `${year}-${month}-${day}`;
    }
}

function checkValidTime(dateStr, startTime, endTime = "", type) {
    const [datePart, timePart] = dateStr.split(' ');
    const [d1, m1, y1] = datePart.split('/').map(Number);
    const [h1, min1, s1] = timePart.split(':').map(Number);
    const date = new Date(y1, m1 - 1, d1, h1, min1, s1);

    const [d2, m2, y2] = startTime.split('/').map(Number);
    const start = new Date(y2, m2 - 1, d2, 0, 0, 0);
    const [d3, m3, y3] = endTime.split('/').map(Number);
    const end = new Date(y3, m3 - 1, d3, 23, 59, 59);

    return type === "valid" ? date >= start && date <= end : date < start;
}

window.addEventListener("DOMContentLoaded", (event) => {
    handleDropdown();

    let rankTimeFilterTimer = null;

    const filter = screen.width < 1200 ? $('.rank-filter-mb') : $('.rank-filter-pc');
    const rankContainer = $('.rank-container');
    const rankListContainer = $('.rank-list-container');
    const rankEmptyContainer = $('.rank-empty-container');
    const rankBottomContainer = $('.rank-bottom-container');
    const rankLoadingContainer = $('.rank-loading-container');
    const topRankContent = $('.top-rank-content');
    const rankTimeItem = filter.find('.dropdown-item');
    const dropdownButton = filter.find('.dropdown-button');
    const dropdownList = filter.find('.dropdown-list');
    const { active, status, start, current } = rankContainer.data();

    async function handleGetList (start, end, type) {
        const cookies = getCookies();
        
        rankLoadingContainer.addClass('flex').removeClass('hidden');
        filter.addClass('pointer-events-none');

        try {
            const resList = await fetchData(`/top-racing?startTime=${start}&endTime=${end}`, "", {}, "", "");

            if (resList?.status === "OK") {
                if (type === "valid") {
                    if (!cookies || !cookies.user) {
                        rankBottomContainer.attr('data-status', 'login');
                    } else {
                        rankBottomContainer.attr('data-status', 'auth');
                    }
                }

                if (resList?.data?.list?.length > 0) {
                    const topRankList = resList?.data?.list?.slice(0, 3) ?? [];
                    const restRankList = resList?.data?.list?.slice(3) ?? [];
                    const restRankPosition = resList?.data?.position ?? null;

                    if (restRankList.length > 0) {
                        rankListContainer.empty();

                        restRankList?.forEach(function(item, index) {
                            let indexItem = index + 4;
                            let data = item;
                            let activeClass = '';

                            if (cookies && cookies?.user && restRankPosition && restRankPosition?.index === indexItem) {
                                data = restRankPosition;
                                activeClass = 'active';
                            }
                            rankListContainer.append(window.createRankItem(data, indexItem, activeClass));
                        });

                        rankEmptyContainer.addClass('hidden').removeClass('flex');
                        rankListContainer.addClass('flex').removeClass('hidden');
                    } else {
                        rankListContainer.addClass('hidden').removeClass('flex');
                        rankEmptyContainer.addClass('flex active-empty').removeClass('hidden active-before');
                    }

                    if (cookies && cookies?.user && restRankPosition) {
                        $('.rank-bottom-index').text(restRankPosition?.index > 500 ? '500+' : restRankPosition?.index);
                        $('.rank-bottom-name').text(restRankPosition?.username);
                        $('.rank-bottom-amount').text(formatMoney(restRankPosition?.amount) + ' K');
                        $('.rank-bottom-turnover').text(formatMoney(restRankPosition?.turnover) + ' VND');
                    }

                    for (let i = 0; i < 3; i++) {
                        const data = topRankList[i];

                        $(`.top-rank-name-${i + 1}`).text(data?.username ? data?.username : "--");
                        $(`.top-rank-amount-${i + 1}`).text(data?.amount ? formatMoney(data?.amount) + ' K' : "--");
                    }
                } else {
                    topRankContent.text('--'); 
                    rankListContainer.addClass('hidden').removeClass('flex');
                    rankEmptyContainer.addClass('flex active-empty').removeClass('hidden active-before');

                    if (cookies && cookies?.user) {
                        $('.rank-bottom-index').text('--');
                        $('.rank-bottom-name').text('--');
                        $('.rank-bottom-amount').text('--');
                        $('.rank-bottom-turnover').text('--');
                    }
                }

                rankLoadingContainer.addClass('hidden').removeClass('flex');
                filter.removeClass('pointer-events-none');
            } else {
                topRankContent.text('--');
                rankListContainer.addClass('hidden').removeClass('flex');
                rankEmptyContainer.addClass('flex active-empty').removeClass('hidden active-before');
                rankLoadingContainer.addClass('hidden').removeClass('flex');
                filter.removeClass('pointer-events-none');

                if (type === "valid") {
                    if (!cookies || !cookies.user) {
                        rankBottomContainer.attr('data-status', 'login');
                    } else {
                        rankBottomContainer.attr('data-status', 'auth');
                    }
                } else {
                    rankBottomContainer.attr('data-status', type);
                }
            }
        } catch (error) {
            topRankContent.text('--');
            rankListContainer.addClass('hidden').removeClass('flex');
            rankEmptyContainer.addClass('flex active-empty').removeClass('hidden active-before');
            rankLoadingContainer.addClass('hidden').removeClass('flex');
            filter.removeClass('pointer-events-none');

            if (type === "valid") {
                if (!cookies || !cookies.user) {
                    rankBottomContainer.attr('data-status', 'login');
                } else {
                    rankBottomContainer.attr('data-status', 'auth');
                }
            } else {
                rankBottomContainer.attr('data-status', type);
            }
        }
    }

    function handleCountdown(startStr, endStr) {
        const [startDatePart, startTimePart] = startStr.split(' ');
        const [startDay, startMonth, startYear] = startDatePart.split('/').map(Number);
        const [startHour, startMinute, startSecond] = startTimePart.split(':').map(Number);
        const startDate = new Date(startYear, startMonth - 1, startDay, startHour, startMinute, startSecond);

        const [endDay, endMonth, endYear] = endStr.split('/').map(Number);
        const endDate = new Date(endYear, endMonth - 1, endDay, 0, 0, 0);

        if (startDate >= endDate) {
            return;
        }

        let remaining = endDate.getTime() - startDate.getTime();

        function formatTime(ms) {
            const totalSeconds = Math.floor(ms / 1000);
            const days = Math.floor(totalSeconds / (60 * 60 * 24));
            const hours = Math.floor((totalSeconds % (60 * 60 * 24)) / (60 * 60));
            const minutes = Math.floor((totalSeconds % (60 * 60)) / 60);
            const seconds = totalSeconds % 60;

            return `${days > 0 && days < 10 ? '0' + days : days} ngày ${hours} giờ ${minutes} phút ${seconds} giây`;
        }

        async function updateCountdown() {
            if (remaining <= 0) {
                clearInterval(countdownRankTimer);

                let formatStart = "";
                let formatEnd = "";

                rankTimeItem.each(function (index, item) {
                    if ($(item).hasClass('selected')) {
                        const { value } = $(item).data();
                        const [startTime, endTime] = value.split('.');
                        formatStart = formatDateTimeToDDMMYYYY(startTime, '');
                        formatEnd = formatDateTimeToDDMMYYYY(endTime, '');
                    }
                })

                await handleGetList(formatStart, formatEnd, 'valid');
                return;
            }

            $('.rank-countdown').text(formatTime(remaining));

            remaining -= 1000;
        }

        updateCountdown();
        countdownRankTimer = setInterval(updateCountdown, 1000);
    }

    if (rankTimeItem.length > 0) {
        rankTimeItem.each(function(index, item) {
            $(item).on('click', async function () {
                clearInterval(countdownRankTimer);
                clearTimeout(rankTimeFilterTimer);

                const target = $(this);
                const { value } = target.data();
                const [startTime, endTime] = value.split('.');
                const formatStart = formatDateTimeToDDMMYYYY(startTime, '');
                const formatEnd = formatDateTimeToDDMMYYYY(endTime, '');

                rankTimeFilterTimer = setTimeout(async () => {
                    rankLoadingContainer.addClass('flex').removeClass('hidden');
                    filter.addClass('pointer-events-none');

                    try {
                        const res = await fetchData('/home/<USER>', "", {}, "", "");
                        const currentTime = formatDateTimeToDDMMYYYY(res?.data ?? "", 'with-hours');
                        const validTime = checkValidTime(currentTime, startTime, endTime, 'valid');

                        if (validTime) {
                            await handleGetList(formatStart, formatEnd, 'valid');
                        } else {
                            const beforeTime = checkValidTime(currentTime, startTime, endTime, 'before');

                            if (beforeTime) {
                                topRankContent.text('--');  
                                rankListContainer.addClass('hidden').removeClass('flex');
                                rankEmptyContainer.addClass('flex active-before').removeClass('hidden active-empty');
                                rankBottomContainer.attr('data-status', 'before');
                                rankLoadingContainer.addClass('hidden').removeClass('flex');
                                filter.removeClass('pointer-events-none');
                                handleCountdown(currentTime, startTime);
                            } else {
                                await handleGetList(formatStart, formatEnd, 'expire');
                                rankBottomContainer.attr('data-status', 'expire');
                            }
                        }
                    } catch (error) {
                        topRankContent.text('--');
                        rankListContainer.addClass('hidden').removeClass('flex');
                        rankEmptyContainer.addClass('flex active-empty').removeClass('hidden active-before');
                        rankLoadingContainer.addClass('hidden').removeClass('flex');
                        filter.removeClass('pointer-events-none');
                    }
                }, 200);
            })

            if (index === Number(active)) {
                $(item).addClass('selected');
            }
        })
    }

    if (status === "before") {
        handleCountdown(current, start);
    }

    dropdownButton.on("click", function () {
        const listWrap = dropdownList.find(".dropdown-list-wrap");
        const selectedItem = listWrap
            .find(".selected")
        if (selectedItem.length) {
            listWrap.scrollTop(
                selectedItem.position().top + listWrap.scrollTop()
            );
        } else {
            listWrap.scrollTop(0);
        }
    });
})
