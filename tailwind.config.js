/** @type {import('tailwindcss').Config} */
import schedule from './tailwind.schedule.config.js';
export default {
    content: [
        "./resources/**/*.blade.php",
        "./resources/**/*.js",
        "./resources/**/*.vue",
        "./Modules/**/*.blade.php",
        "./Modules/**/*.js",
        "./Modules/**/*.php",
        ...schedule.content,
    ],
    theme: {
        container: {
            // you can configure the container to be centered
            center: true,
            padding: {
                DEFAULT: "12px",
                sm: "0.5rem",
                lg: "1rem",
                xl: "1rem",
                "2xl": "0.625rem",
            },
            screens: {
                sm: "100%",
                md: "100%",
                lg: "100%",
                xl: "100%",
                "2xl": "1260px",
            },
        },
        screens: {
            "2xl": "1535px",
            // => @media (min-width: 1535px) { ... }

            xl: "1200px",
            // => @media (min-width: 1200px) { ... }

            lg: "1023px",
            // => @media (min-width: 1023px) { ... }

            md2: "992px",
            // => @media (min-width: 992px) { ... }

            md1: "820px",
            // => @media (min-width: 992px) { ... }

            md: "767px",
            // => @media (min-width: 767px) { ... }

            sm: "639px",
            // => @media (min-width: 639px) { ... }

            xs: "400px",
            // => @media (min-width: 400px) { ... }

            xxs: "390px",
            // => @media (min-width: 390px) { ... }
        },
        fontFamily: {
            sans: ["Inter", "sans-serif"], // Add Inter to the sans font family
            redzone: ["SVN_VT_Redzone", "sans-serif"],
            open: ["Open Sans"],
            montserrat: ["Montserrat"],
            utm: ['"UTM Facebook"', 'sans-serif'],
            helvetica: ["SVN-Helvetica Neue Heavy", "sans-serif"]
        },
        extend: {
            backgroundImage: {
                ...schedule.theme?.extend?.backgroundImage || {},
                "header-gradient":
                    "linear-gradient(90deg, #F9FBFC 37.77%, #F9EAE8 72.2%, #F9FBFC 100%)",
                "header-gradient-menu":
                    "linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #FFFFFF 100%)",
                "black-gradient":
                    "linear-gradient(180deg, rgba(23, 23, 23, 0.00) 0%, rgba(23, 23, 23, 0.80) 50%, #171717 100%)",
                "blue-3-gradient":
                    "linear-gradient(0deg, #E5F2FF 0%, #FFFFFF 100%)",
                "blue-1-gradient":
                    "linear-gradient(142deg, rgba(207, 219, 249, 0.50) 0%, #CFDBF9 100%)",
                "orange-gradient":
                    "linear-gradient(135deg, #FF5D06 0%, #FF9D00 100%)",
                "red-gradient":
                    "linear-gradient(135deg, #FF0606 0%, #FF39A6 100%)",
                "blue-gradient":
                    "linear-gradient(135deg, #00AE91 0%, #1B8ED6 100%)",
                "blue-2-gradient":
                    "linear-gradient(186deg, rgba(207, 219, 249, 0.50) 0%, #CFDBF9 100%)",
                "sport-bg-gradient":
                    "linear-gradient(218deg, #A3CFFF 0%, #064991 100%)",
                "blue-stroke-gradient":
                    "linear-gradient(0deg, #FFFAF2 0%, #FFFFFF 100%)",
                "red-stroke-gradient":
                    "linear-gradient(180deg, #FF7272 0%, #FFDCDC 100%)",
                "orange-stroke-gradient":
                    "linear-gradient(180deg, #FFBC03 0%, #FFE293 100%)",
                "light-blue-gradient":
                    "linear-gradient(218deg, #F5F5F7 0%, #EFF3F8 100%)",
                "pink-gradient":
                    "linear-gradient(180deg, #D53CD4 0%, rgba(213, 60, 212, 0.00) 100%)",
                "purple-gradient":
                    "linear-gradient(180deg, #B492FF 0%, rgba(180, 146, 255, 0.00) 100%)",
                "gradient-jackpot":
                    "linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.2) 100%)",
                "jackpot-gradient-2":
                    "linear-gradient(182.29deg, #003B5B 2.05%, rgba(3, 26, 52, 0.2) 103.5%)",
                "gradient-jackpot-money":
                    "linear-gradient(180deg, #FFD362 23.75%, #FFE6A8 48.63%, #FFD362 48.69%, #FFD362 76.25%)",
                "gradient-menu":
                    "linear-gradient(360deg, #A6E7FF -3.57%, rgba(166, 231, 255, 0.1) 100%)",
                "forget-pass-note": "radial-gradient(103.59% 103.59% at 3.24% 0%, #FEF0E3 0%, #FFFFFF 100%)",
                "gradient-process-bar" : "linear-gradient(95.45deg, #E6F5FF -3.86%, #B0E0FF 102.49%)",
                "card-amount":
                    "radial-gradient(99.3% 105.95% at 49.63% 0%, #FFFFFF 0%, #F1F3F6 100%)",
                "card-amount-active":
                    "radial-gradient(79.51% 100% at 49.63% 0%, #FFFFFF 19.24%, #FFFAF2 100%)",
                "bank-account":
                    "linear-gradient(223.92deg, #FCFCFC -9.02%, #F2F2F2 67.85%)",
                "sports-gradient":
                    "linear-gradient(90.42deg, #F6F8FA 18.03%, #E0DFE4 67.69%, #ED99A0 99.92%)",
                "game-filter-item":
                    "radial-gradient(75% 85% at 50% 50%, #FFFFFF 0%, #E9E9E9 100%)",
                "topjackpot-gradient":
                    "linear-gradient(222.89deg, #FFFFFF 20.74%, #F8F8F8 79.26%)",
                "news-gradient":
                    "linear-gradient(103.19deg, #F6F8FA 13.74%, #F6E4E4 36.62%, #F6F8FA 68.56%)",
                "home-live-gradient": "linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.3) 33.79%, rgba(0, 0, 0, 0.7) 100%)",
                "guide-item":
                    "linear-gradient(0deg, #FFFAF2 0%, #FFFFFF 100%)",
                "promotion-progress":
                    "linear-gradient(98.27deg, #FFE6CA -23.47%, #FF8C1A 119.85%)",
                "left-menu-item":
                    "radial-gradient(62.07% 66.12% at 66.18% 35%, #FFFFFF 0%, #F4F4F4 100%)",
                "help-menu":
                    "linear-gradient(180deg, #F5F5F5 0%, #FFFFFF 100%)",
                "overview-wallet":
                    "linear-gradient(90deg, #FFFBEE 45.71%, #FFD4D1 100%)",
                "overview-menu-item":
                    "linear-gradient(131.42deg, #F5F8FF 1.59%, #E4E9F0 50.02%, #F5F8FF 98.41%)",
                "event": 
                    "linear-gradient(270deg, rgba(255, 249, 249, 0) 0%, rgba(255, 235, 235, 0.5) 50%, rgba(255, 249, 249, 0) 100%), radial-gradient(49.41% 38.33% at 50.59% 100%, rgba(255, 201, 201, 0.66) 0%, rgba(255, 201, 201, 0) 100%), radial-gradient(14.89% 47.16% at 50.59% 100%, rgba(255, 201, 201, 0.66) 0%, rgba(255, 201, 201, 0) 100%)",
                "event-deposit-section":
                    "linear-gradient(283.33deg, #FFB9B9 5.19%, #FFCCCC 20.69%, #FED8D7 58.43%, #FFD8D4 90.42%)",
                "event-gift-line":
                    "linear-gradient(86.71deg, #FFE0CA 0.4%, #FFDFD2 55.79%, #CD1717 97.27%)",
                "event-table-header":
                    " linear-gradient(59.79deg, #DC3038 -17.31%, #FF6E74 102.84%)",
                "event-list":
                    "linear-gradient(90deg, #FFE2D9 0%, #FFE6D0 100%)",
                "event-index":
                    "linear-gradient(180deg, #FF3131 22.58%, #A20000 100%)",
                "event-rule-line":
                    "linear-gradient(86.87deg, #F0B35E 0.27%, #FFDE8E 37.88%, #F8F3F3 97.39%)",
                "event-rule-bg":
                    "linear-gradient(108.92deg, #F6E4E4 -5.33%, #F8F8F8 108.25%)",
                "event-rule-tag":
                    "linear-gradient(86.87deg, #F3DBBA 0.27%, #FFD9B9 97.39%)",
                "event-rule-tag-active":
                    "linear-gradient(86.87deg, #FFDE8E 0.27%, #F0B35E 97.39%)",
                "event-progress":
                    "linear-gradient(96.42deg, #FF5959 41.14%, #FFB14C 96.61%)",
                "applicable-games-bg":
                    " linear-gradient(90deg, #FFE9E9 0%, rgba(255, 233, 233, 0) 100%)",
                "top-racing-term":
                    "linear-gradient(110.84deg, #F8F8F8 -2.44%, #F6E4E4 108.01%)",
                "rank-list":
                    "linear-gradient(185.46deg, #F6E4E4 1.79%, #F8F8F8 101.68%)",
                "rank-list-bottom":
                    "linear-gradient(108.92deg, #F2CBCB -5.33%, #F4EDED 108.25%)",
                "rank-list-header-table":
                    "linear-gradient(90.01deg, #FFB2AA 0.01%, #FFB7B5 100%)",
            },
            boxShadow: {
                ...schedule.theme?.extend?.boxShadow || {},
                "footer-button-dropdown": "0px -2px 10px 0px #********",
                "shadow-4px": "0px 2px 4px 0px #2221211A",
                "shadow-bank": "0px 4px 10.8px 0px #48484D26",
                "shadow-status-list": "0px 2px 4px 0px #2221211A",
                "shadow-status-list-mb": "0px 4px 10.8px 0px #48484D26",
                "hotmatch-card": "7px 6px 39.7px 0px #FFFFFF inset",
                "rank-bottom": "0px -2px 8px 0px #0000001A"
            },
            spacing: {
                desktop: "1240px",
            },
            borderRadius: {
                "cor-card-desktop": "0.5rem",
                "cor-card-mobile": "0.25rem",
                "cor-max": "50rem",
            },
            colors: {
                ...schedule.theme?.extend?.colors || {},
                "btn-tertiary-disable": {
                    DEFAULT: "#F5F5F7",
                },
                "qrcode-content": "#C19B5533",
                "promotion-bet": "#0FC434",
                "auth-close":{
                    DEFAULT: "#FFFFFF4D",
                    100: "#ffffffc5",
                },
                primary: {
                    50: "#FFFAF2",
                    100: "#FFEDCD",
                    200: "#FFE0A8",
                    300: "#FBD081",
                    400: "#DEB56A",
                    500: "#C19B55",
                    525: "#C19B5540",
                    600: "#A48142",
                    700: "#876932",
                    800: "#6A5123",
                    900: "#4C3917",
                    1000: "#F8FAFBCC",
                },
                secondary: {
                    50: "#FFECED",
                    100: "#FFC2C5",
                    200: "#FF989D",
                    300: "#FF6E74",
                    400: "#FE444C",
                    500: "#DC3038",
                    600: "#BA2026",
                    700: "#981218",
                    800: "#76090D",
                    900: "#540205",
                    1000: "#004B9B99",
                    1100: "#997B51"
                },
                tertiary: {
                    50: "#FDFFF0",
                    100: "#F8FFD0",
                    200: "#F4FFAD",
                    300: "#F0FF85",
                    400: "#EEFF6E",
                    500: "#EBFF1E",
                    600: "#CCDE19",
                    700: "#AEBE13",
                    800: "#758009",
                    900: "#414803",
                },
                quaternary: {
                    50: "#DDEBF8",
                    100: "#C6DEF3",
                    200: "#AFD1EE",
                    300: "#A4CAEB",
                    400: "#98C4E9",
                    500: "#8DBDE6",
                    600: "#7FAACF",
                    700: "#7197B8",
                    800: "#6384A1",
                    900: "#55718A",
                    1000: "#672029",
                    1100: "#7E5200",
                    1200: "#82252B"
                },
                neutral: {
                    DEFAULT: "#FFFFFF",
                    50: "#F5F5F7",
                    70: "#212020B2",
                    100: "#EFF1F6",
                    150: "#E7E9EE",
                    180: "#E4E9F0",
                    200: "#E2E3E9",
                    250: "#F9FAFB",
                    300: "#CFD1D9",
                    400: "#BDC0C8",
                    500: "#AAAEB7",
                    600: "#979CA7",
                    700: "#979CA7",
                    800: "#636975",
                    850: "#575D6A",
                    900: "#4C5057",
                    950: "#32353B",
                    1000: "#2A2B2E",
                    1050: "#3A414A",
                    1200: "#0D0D12",
                    1500: "#21262C",
                    1550: "#1F1F1F",
                    1600: "#0078FF",
                    1650: "#333333",
                    1700: "#757575",
                    1750: "#D8DADC",
                    1800: "#36394A",
                    1900: "#393D42"
                },
                danger: {
                    50: "#FF0044E5",
                    100: "#FEE2E2",
                    200: "#FECACA",
                    300: "#FCA5A5",
                    400: "#F87171",
                    500: "#EF4444",
                    600: "#DC2626",
                    700: "#B91C1C",
                    800: "#991B1B",
                    900: "#7F1D1D",
                    1000: "#B93D27"
                },
                info: {
                    100: "#DBEAFE",
                    200: "#BFDBFE",
                    250: "#94C7F0",
                    300: "#93C5FD",
                    400: "#60A5FA",
                    500: "#3B82F6",
                    600: "#2563EB",
                    700: "#1D4ED8",
                    800: "#1E40AF",
                    900: "#1E3A8A",
                },
                success: {
                    100: "#DCFCE7",
                    200: "#BBF7D0",
                    300: "#86EFAC",
                    400: "#4ADE80",
                    500: "#22C55E",
                    600: "#16A34A",
                    700: "#15803D",
                    800: "#166534",
                    900: "#14532D",
                },
                warning: {
                    100: "#FEF9C3",
                    200: "#FEF08A",
                    300: "#FDE047",
                    400: "#FACC15",
                    500: "#EAB308",
                    600: "#CA8A04",
                    700: "#9B5C02",
                    800: "#854D0E",
                    900: "#713F12",
                    1000: "#F6B2B2",
                    1100: "#FEF9DA"
                },
                surface: {
                    "sur-primary": "#FFFFFF",
                    "sur-secondary": "#F5F5F7",
                    "sur-tertiary": "#EFF1F6",
                    "sur-quaternary": "#E7E9EE",
                },
                alert: {
                    "sur-success-light": "#BBF7D0",
                    "sur-success-dark": "#15803D",
                    "sur-warning-light": "#FEF08A",
                    "sur-warning-dark": "#9B5C02",
                    "sur-error-light": "#FECACA",
                    "sur-error-dark": "#B91C1C",
                    "error": "#F71B26",
                },
                device: {
                    "bg-desktop": "#EFF1F6",
                    "bg-mobile": "#EFF1F6",
                },
                brand: {
                    "bg-primary-brand": "#C19B55",
                    "bg-secondary-brand": "#DC3038",
                    "bg-bank-brand": "#FE444C",
                    "bg-footer": "#F5F5F7",
                },
                "count-items": {
                    DEFAULT: "#FFFFFF33",
                },
                black: {
                     DEFAULT: "#000000",
                    5: "#0000000D",
                    50: "#********",
                    70: "#242425B2",
                    80: "#********",
                    90: "#********",
                    350: "#979FB0",
                    400: "#717589",
                    600: "#3D3F4D",
                    650: "#494C55",
                    700: "#242425B2",
                    800: "#56585F",
                    900: "#27272A",
                },
                "functional-success": {
                    DEFAULT: '#0D8E01',
                },
                white: {
                    5: "#FFFFFF0D",
                    30: "#FFFFFF4D",
                    50: "#FCFCFC80",
                    '50-2': "#FFFFFF80",
                    '50-2-2': "#FFFFFFBF",
                },
                green: {
                    300: "#00FF95",
                },
                gray: {
                    150:'#D5D7D9',
                    300: "#B1B9CB",
                    400: "#3E4972"
                },
                gold: {
                    500: "#F2D49A",
                    550: "#FFB631",
                    600: "#FFC702",
                },
                scrollbar: {
                    DEFAULT: "#EFF1F6",
                },
                note: {
                    DEFAULT: "#FFEEDD",
                },
            },
            fontSize: {
                xs: [
                    "12px",
                    {
                        lineHeight: "16px",
                    },
                ],
            },
            filter: {
                secondary: "brightness(0) saturate(100%) invert(93%) sepia(12%) saturate(113%) hue-rotate(190deg) brightness(89%) contrast(92%)",
            },
        },
    },
    plugins: [],
};
