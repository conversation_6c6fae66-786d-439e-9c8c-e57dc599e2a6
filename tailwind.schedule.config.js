/** @type {import('tailwindcss').Config} */
module.exports = {
    content: [
        './Modules/MatchSchedule/**/*.blade.php',
        './Modules/MatchSchedule/**/*.js',
    ],
    theme: {
        extend: {
            backgroundImage: {
                "match-gradient":"linear-gradient(90deg, #FFECD5 45.19%, rgba(255, 236, 213, 0) 100%)"
            },
            colors: {
                'match-primary': {
                    DEFAULT: '#FFFFFF',
                    50: "#FFF6ED",
                    100: "#FFECD5",
                    200: "#FDD4AB",
                    300: "#FCB675",
                    400: "#F98C3E",
                    500: "#F77221",
                    "content": '#2A2B2E',
                },
                'match-secondary': {
                    DEFAULT: '#575D6A',
                    50: "#E0F1FE",
                    100: "#BAE4FD",
                    200: "#7DD0FC",
                    300: "#21B0F7",
                    400: "#0EA0E9",
                    500: "#027FC7",
                    "content": '#504E4E',
                    "surface": '#F5F5F7',
                    "tertiary": '#EFF1F6',
                },
                'match-brand':{
                    'primary-light':"#876932"
                },
                'match-success': {
                    DEFAULT: '#0F0F0F',
                    50: '#E7F0EE',
                    100: '#B7D3CC',
                    200: '#87B5AB',
                    300: '#579789',
                    400: '#277A67',
                    500: '#0F6B56',
                },
                'match-desktop': {
                    DEFAULT: '#EFF1F6',
                },
                'match-brand-primary': {
                    DEFAULT: '#0F6B56',
                },
                'match-alert-status': {
                    warning: '#FACC15',
                    success: '#0F6B56',
                    error: '#B91C1C',
                },
            },
            boxShadow: {
                '4': '0px 2px 4px 0px #2221211A',
                "16":"0px 10px 16px 0px #68686840",
                '32': '0px 4px 32px 0px #0000000A',
            },
        },
    },
    plugins: [],
}
