@php
    $navList = config('header.navList');
    $mbHiddenLinks = config('header.mbHiddenLinks');

    $isOverview = request()->url() === route('en.account.index') 
                && request()->get('tab') === 'overview';
                
    $isHideMbHeader = in_array(Route::currentRouteName(), $mbHiddenLinks) || $isOverview;
@endphp

<header class="sticky top-0 z-[52] bg-neutral xl:border-b-0 border-b border-neutral-150 {{$isHideMbHeader ? 'hidden xl:block' : 'block'}} " x-data="{}">
    <div class="container {{$isHideMbHeader ? 'hidden xl:flex' : 'flex'}} justify-between items-center gap-[20px] h-[48px] py-[10px] mx-auto xl:h-[72px] xl:py-4">
        <a href='/'>
            <img alt="logo" src="{{ asset('asset/images/brand/logo.svg') }}" class="w-[100px] h-[32px] xl:w-[145px] xl:h-[46px]"/>
        </a>
        <div class="js-header-auth gap-[10px] xl:gap-3 {{ Auth::check() ? "flex" : "hidden" }}">
            <x-kit.badges></x-kit.badges>
            <div class="flex cursor-pointer gap-2 h-[28px] max-w-[135px] py-[4px] pl-[8px] pr-[7px] bg-neutral-150 xl:hover:bg-neutral-200 rounded-[6px] xl:rounded-[8px] xl:h-[40px] xl:p-[4px]">
                <a href="/account" aria-label="account" class="hidden h-[32px] w-[32px] rounded-lg xl:block">
                    <img alt="avatar" src="{{ asset('asset/images/header/avatar-header.avif') }}" class="w-full h-full"/>
                </a>
                <div class="flex items-center gap-2 xl:w-[calc(100%-40px)]">
                    <div class="flex flex-col w-full">
                        <a href="/account" aria-label="account" class="js-fullname-account hidden max-w-full text-[12px] truncate leading-[18px] font-normal text-neutral-800 line-clamp-1 xl:block">
                            {{ Auth::user()->fullname ?? (Auth::user()->username ?? '') }}
                        </a>
                        <a href="/account" aria-label="account" class="js-account-balance text-[12px] leading-[18px] font-medium text-primary-700">
                            {{ number_format(Auth::user()->balance ?? 0, 0, '.', ',') }} K
                        </a>
                    </div>
                    <a href="/account/deposit" class="flex justify-center items-center min-w-[20px] h-[20px] bg-primary-500 rounded-full cursor-pointer xl:hidden">
                        <i class="icon-plus text-[14px] text-neutral"></i>
                    </a>
                </div>
            </div>
            <x-kit.button aria-label="nap tien" onclick="window.location.href='/account/deposit'" button-type="button" style="filled"
                type="primary" size="large" class="hidden min-w-[96px] rounded-full xl:flex">Nạp Tiền</x-kit.button>
        </div>
        <div class="js-header-login gap-[10px] xl:gap-3 {{ Auth::check() ? "hidden" : "block" }}">
            <div class="flex gap-[8px] xl:hidden">
                <x-kit.button button-type="button" onclick="openLogin(); handleClearSession()" style="out-line" type="primary" size="small" class="min-w-[88px] text-primary-700 border border-primary-500 rounded-full">Đăng
                    Nhập</x-kit.button>
                <x-kit.button button-type="button" onclick="openSignup(); handleClearSession()" style="filled" type="primary" size="small" class="min-w-[88px] rounded-full">Đăng
                    Ký</x-kit.button>
            </div>
            <form id="login-form" class="hidden gap-4 xl:flex">
                @csrf
                <div class="flex gap-2">
                    <x-kit.button button-type="button" onclick="openLogin(); handleClearSession()" style="out-line" type="primary" size="large"
                        class="min-w-[111px] rounded-full text-primary-700 border-2 border-primary-500 xl:hover:border-primary-400">Đăng Nhập</x-kit.button>
                    <x-kit.button button-type="button" onclick="openSignup(); handleClearSession()" style="filled" type="primary" size="large"
                        class="js-btn-login-register rounded-full min-w-[92px]">Đăng Ký</x-kit.button>
                </div>
            </form>
        </div>
    </div>
    <div class="hidden justify-center w-full h-[60px] bg-header-gradient xl:flex">
        <div class="container flex justify-between gap-4 w-full h-full max-[1250px]:gap-2">
            @foreach ($navList as $navItemIndex => $navItem)
                @if (isset($navItem['activeList']))
                    @php
                        $isActive = (in_array(request()->getPathInfo(), $navItem['activeList']) && $navItemIndex !== count($navList) - 1) || ($navItemIndex === count($navList) - 1 && (request()->getPathInfo() === $navItem['href'] || in_array(request()->getPathInfo(), $navItem['activeList'])));
                    @endphp
                    <div 
                        @class([
                            '[&:hover_.nav-list]:h-[145px] [&:hover_.nav-list]:pt-[23px] menu-item',
                            'active-link font-medium' => $isActive,
                        ])
                    >

                        @if (isset($navItem['href']) && $navItem['href'])
                            <a 
                                class="relative flex items-center px-[10px] h-full text-[16px] leading-[24px] font-medium text-neutral-1000 cursor-pointer [.active-link_&]:bg-header-gradient-menu [.menu-item:hover_&]:bg-header-gradient-menu"
                                href="{{ $navItem['href'] }}" >
                                <p class="flex items-center gap-1 [.active-link_&]:text-primary-600 [.menu-item:hover_&]:text-primary-600">
                                    <img src="{{ asset($navItem['icon']) }}" alt="icon-nav" class="size-[24px]" loading="lazy" fetchpriority="low">
                                    {{ $navItem['label'] }}
                                </p>
                                <span class="absolute bottom-0 left-1/2 -translate-x-1/2 hidden w-[30px] h-[5px] bg-primary-500 [.active-link_&]:block [.menu-item:hover_&]:block"></span>
                                <x-kit.label type="{{ $navItem['labelType'] }}" size="large" class="absolute top-0 right-[-5px]" hotSize="true"></x-kit.label>
                            </a>

                        @else
                            <div
                                class="relative flex items-center px-[10px] h-full text-[16px] leading-[24px] font-medium text-neutral-1000 cursor-pointer [.active-link_&]:bg-header-gradient-menu [.menu-item:hover_&]:bg-header-gradient-menu"
                            >
                                <p class="flex items-center gap-1 [.active-link_&]:text-primary-600 [.menu-item:hover_&]:text-primary-600">
                                    <img src="{{ asset($navItem['icon']) }}" alt="icon-nav" class="size-[24px]" loading="lazy" fetchpriority="low">
                                    {{ $navItem['label'] }}
                                </p>
                                <span class="absolute bottom-0 left-1/2 -translate-x-1/2 hidden w-[30px] h-[5px] bg-primary-500 [.active-link_&]:block [.menu-item:hover_&]:block"></span>
                                <x-kit.label type="{{ $navItem['labelType'] }}" size="large" class="absolute top-0 right-[-5px]" hotSize="true"></x-kit.label>
                            </div> 
                        @endif

                        @if (isset($navItem['list']))
                            <div class="nav-list absolute top-[132px] left-0 flex justify-center items-start z-30 gap-[60px] w-full h-0 pt-0 bg-neutral-70 backdrop-blur-[3px] overflow-hidden transition-all duration-300">
                                @foreach ($navItem['list'] as $item)
                                    <button
                                        type="button"
                                        onclick="openGameHeaderItem({ link: '{{ $item['link'] }}', requiredLogin: '{{ $item['requiredLogin'] }}' })"
                                        aria-label="{{ $item['label'] }}"
                                        @class([
                                            'relative flex flex-col justify-center items-center gap-[2px] min-w-[100px] h-[100px] px-[3px] rounded-[8px] overflow-hidden cursor-pointer [&:hover_.nav-active-bg]:block [&.active-link_.nav-active-bg]:block',
                                            'active-link' => request()->getPathInfo() === $item['link'],
                                        ])
                                    >
                                        <img src="{{ asset($item['icon']) }}" alt="icon-nav" class="relative z-[1] w-[70px] h-[70px]" loading="lazy" fetchpriority="low"/>
                                        <p class="relative z-[1] text-[14px] leading-[20px] text-neutral uppercase font-medium">
                                            {{ $item['label'] }}
                                        </p>
                                    </button>
                                @endforeach
                            </div>
                        @endif
                    </div>
                @else
                    <a 
                        href="{{ $navItem['href'] }}"
                        @class([
                            '[&:hover_.nav-list]:flex menu-item',
                            'active-link font-medium' => request()->getPathInfo() === $navItem['href'],
                        ])
                    >
                        <div class="relative flex items-center px-[10px] h-full text-[16px] leading-[24px] font-medium text-neutral-1000 cursor-pointer [.active-link_&]:bg-header-gradient-menu [.menu-item:hover_&]:bg-header-gradient-menu">
                            <p class="flex items-center gap-1 [.active-link_&]:text-primary-600 [.menu-item:hover_&]:text-primary-600">
                                <img src="{{ asset($navItem['icon']) }}" alt="icon-nav" class="size-[24px]" loading="lazy" fetchpriority="low">
                                {{ $navItem['label'] }}
                            </p>
                            <span class="absolute bottom-0 left-1/2 -translate-x-1/2 hidden w-[30px] h-[5px] bg-primary-500 [.active-link_&]:block [.menu-item:hover_&]:block"></span>
                            <x-kit.label type="{{ $navItem['labelType'] }}" size="large" class="absolute top-0 right-[-5px]" hotSize="true"></x-kit.label>
                        </div>
                    </a>
                @endif
            @endforeach
        </div>
    </div>
</header>

@push('scripts')
    <script>
        window.addEventListener("DOMContentLoaded", (event) => {
            $('.js-username-login input, .js-password-login input').on('input', function() {
                const username = $('.js-username-login input').val();
                const password = $('.js-password-login input').val();
            });
            $('#login-form').on('submit', async function(e) {
                e.preventDefault();
                const username = $('.js-username-login input').val();
                const password = $('.js-password-login input').val();
               
                if (!username || !password) {
                    openModal(errorLoginModal, false, 'error-login-modal');
                    $('#error-login-text').text('Vui lòng nhập tên đăng nhập và mật khẩu');
                } else {
                    const token = await handleRecaptcha();
                    const payload = {
                        username: username,
                        password: password,
                        token: token
                    };
                    if (window.dataLayer && Array.isArray(window.dataLayer)) {
                        window.dataLayer.push({ event: "formSubmitted", formName: "Form_Login" });
                    }
                    
                    res = await submitData(url = '/login', params = payload, apiBase = '', ver = '');
                    if (res.status) {
                        useToast('success', res?.message);

                        // update flow login - giu nguyen trang truoc dang nhap
                        setTimeout(() => window.location.reload(), 1500);

                    } else {
                        openModal(errorLoginModal, false, 'error-login-modal');

                        $('#error-login-text').text(res?.message)
                    }
                }
            });
        });

        const loginModal = `<x-ui.auth-modal :id="'login-modal'"><x-ui.auth.login-form></x-ui.auth.login-form></x-ui.auth-modal>`
        const signupModal = `<x-ui.auth-modal :id="'signup-modal'"><x-ui.auth.signup-form></x-ui.auth.signup-form></x-ui.auth-modal>`
        const forgetModal = `<x-ui.auth-modal :id="'forget-modal'"><x-ui.auth.forget-form></x-ui.auth.forget-form></x-ui.auth-modal>`
        const newPassModal = `<x-ui.auth-modal :id="'new-pass-modal'"><x-ui.auth.new-pass-form></x-ui.auth.new-pass-form></x-ui.auth-modal>`
        const errorLoginModal = `<x-ui.modal :id="'error-login-modal'"><x-ui.auth.error-login type='error'></x-ui.auth.error-login></x-ui.modal>`;
        const errorLoginBlockModal = `<x-ui.modal :id="'error-login-modal'"><x-ui.auth.error-login type='block'></x-ui.auth.error-login></x-ui.modal>`;
        const errorLoginNotfoundModal = `<x-ui.modal :id="'error-login-modal'"><x-ui.auth.error-login type='notfound'></x-ui.auth.error-login></x-ui.modal>`;
        const changeNameModal = `<x-ui.modal :id="'change-name-modal'"><x-ui.auth.change-name></x-ui.auth.change-name></x-ui.modal>`;
        const notiSignupModal = `<x-ui.modal :id="'noti-signup-modal'"><x-ui.auth.noti-signup></x-ui.auth.noti-signup></x-ui.modal>`;
        const rewardEventModal = `<x-ui.reward-event-modal></x-ui.reward-event-modal>`;
        const topRacingEventModal = `<x-ui.top-racing-event-modal></x-ui.top-racing-event-modal>`;
        const topRacingEventGiftModal = `<x-ui.event-modal.top-racing :id="'top-racing-gift-modal'"></x-ui.event-modal.top-racing>`;
        const rewardGoldenHourGiftModal = `<x-ui.event-modal.reward id="reward-golden-hour-gift-modal"><x-ui.event-modal.reward.gift></x-ui.event-modal.reward.gift></x-ui.event-modal.reward>`
        const rewardGoldenHourResultModal = `<x-ui.event-modal.reward id="reward-golden-hour-result-modal"><x-ui.event-modal.reward.result></x-ui.event-modal.reward.result></x-ui.event-modal.reward>`;
        const rewardGoldenHourExpireModal = `<x-ui.event-modal.reward id="reward-golden-hour-expire-modal"><x-ui.event-modal.reward.expire></x-ui.event-modal.reward.expire></x-ui.event-modal.reward>`;
        const finalClubWorldCupModal = `<x-ui.final-club-world-cup-event-modal></x-ui.final-club-world-cup-event-modal>`;
        const finalClubWorldCupInfoModal = `<x-ui.event-modal.final-club-world-cup></x-ui.event-modal.final-club-world-cup>`;
        const finalClubWorldCupGiftModal = `<x-ui.event-modal.final-club-world-cup.gift></x-ui.event-modal.final-club-world-cup.gift>`;
        const taxPolicyModal = `<x-ui.event-modal.tax-policy></x-ui.event-modal.tax-policy>`;
    </script>
@endpush
