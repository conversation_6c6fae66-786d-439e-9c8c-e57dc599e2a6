@props([
    'href' => '',
    'media' => 'all',
    'critical' => false,
    'preload' => false
])

@if($critical)
    {{-- Critical CSS loaded synchronously --}}
    <link rel="stylesheet" href="{{ $href }}" media="{{ $media }}">
@elseif($preload)
    {{-- Preload CSS for better performance --}}
    <link rel="preload" href="{{ $href }}" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="{{ $href }}"></noscript>
@else
    {{-- Load CSS asynchronously --}}
    <link rel="preload" href="{{ $href }}" as="style" onload="this.onload=null;this.rel='stylesheet'" media="{{ $media }}">
    <noscript><link rel="stylesheet" href="{{ $href }}" media="{{ $media }}"></noscript>
@endif

@pushOnce('scripts')
<script>
// Polyfill for browsers that don't support rel="preload"
(function() {
    var supportsPreload = function() {
        try {
            return document.createElement('link').relList.supports('preload');
        } catch (e) {
            return false;
        }
    };

    if (!supportsPreload()) {
        var links = document.querySelectorAll('link[rel="preload"][as="style"]');
        for (var i = 0; i < links.length; i++) {
            var link = links[i];
            link.rel = 'stylesheet';
        }
    }
})();

// CSS loading utility
window.loadCSS = window.loadCSS || function(href, before, media) {
    var doc = window.document;
    var ss = doc.createElement('link');
    var ref;
    if (before) {
        ref = before;
    } else {
        var refs = (doc.body || doc.getElementsByTagName('head')[0]).childNodes;
        ref = refs[refs.length - 1];
    }

    var sheets = doc.styleSheets;
    ss.rel = 'stylesheet';
    ss.href = href;
    ss.media = 'only x';

    function ready(cb) {
        if (doc.body) {
            return cb();
        }
        setTimeout(function() {
            ready(cb);
        });
    }

    ready(function() {
        ref.parentNode.insertBefore(ss, (before ? ref : ref.nextSibling));
    });

    var onloadcssdefined = function(cb) {
        var resolvedHref = ss.href;
        var i = sheets.length;
        while (i--) {
            if (sheets[i].href === resolvedHref) {
                return cb();
            }
        }
        setTimeout(function() {
            onloadcssdefined(cb);
        });
    };

    function loadCB() {
        if (ss.addEventListener) {
            ss.removeEventListener('load', loadCB);
        }
        ss.media = media || 'all';
    }

    if (ss.addEventListener) {
        ss.addEventListener('load', loadCB);
    }
    ss.onloadcssdefined = onloadcssdefined;
    onloadcssdefined(loadCB);
    return ss;
};
</script>
@endPushOnce
