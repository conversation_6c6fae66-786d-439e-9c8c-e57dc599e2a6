@props([
    'src' => '',
    'alt' => '',
    'class' => '',
    'fallback' => '/asset/images/default-image.avif',
    'lazy' => true,
    'width' => null,
    'height' => null,
    'sizes' => null
])

@php
use App\Helpers\ImageHelper;

$sources = ImageHelper::getOptimizedImageSources($src, $fallback);
$imgAttributes = [
    'alt' => $alt,
    'decoding' => 'async'
];

if ($lazy) {
    $imgAttributes['loading'] = 'lazy';
}

if ($width) {
    $imgAttributes['width'] = $width;
}

if ($height) {
    $imgAttributes['height'] = $height;
}

if ($class) {
    $imgAttributes['class'] = $class;
}

if ($sizes) {
    $imgAttributes['sizes'] = $sizes;
}
@endphp

<div class="image-container">
    @if(count($sources) > 1)
        <picture>
            @foreach(array_slice($sources, 0, -1) as $source)
                <source srcset="{{ $source['srcset'] }}" type="{{ $source['type'] }}">
            @endforeach
            
            @php $fallbackSource = end($sources); @endphp
            <img 
                src="{{ $fallbackSource['srcset'] }}" 
                @foreach($imgAttributes as $key => $value)
                    {{ $key }}="{{ $value }}"
                @endforeach
                onerror="this.onerror=null; this.src='{{ $fallback }}';"
            />
        </picture>
    @else
        @php $singleSource = $sources[0] ?? ['srcset' => $fallback]; @endphp
        <img 
            src="{{ $singleSource['srcset'] }}" 
            @foreach($imgAttributes as $key => $value)
                {{ $key }}="{{ $value }}"
            @endforeach
            onerror="this.onerror=null; this.src='{{ $fallback }}';"
        />
    @endif
    
    @if($lazy)
        <div class="skeleton absolute inset-0 bg-gray-200 animate-pulse rounded"></div>
    @endif
</div>

@pushOnce('scripts')
    @vite(['resources/js/optimized-image.js'])
@endPushOnce

<style>
.image-container {
    position: relative;
    overflow: hidden;
}

.image-container img {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.image-container img.loaded {
    opacity: 1;
}

.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

.skeleton.hidden {
    display: none;
}
</style>
