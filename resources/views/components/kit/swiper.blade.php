@props([
    'swiperRequiredClass' => '',
    'swiperWrapperClass' => '',
    'swiperConfig' => [],
    'isHiddenNavigation' => false,
    'isOverflowVisible' => false,
    'paginationClass' => '',
    'swiperSection' => ''
])
@php
    // Simplifying class name handling for pagination and navigation
    $paginationClass = str_replace('.', '', $swiperConfig['pagination']['el'] ?? '');
    $nextNavigationClass = str_replace('.', '', $swiperConfig['navigation']['nextEl'] ?? '');
    $prevNavigationClass = str_replace('.', '', $swiperConfig['navigation']['prevEl'] ?? '');
@endphp

<div class="swiper-section relative {{ $swiperSection }}">
    <div class="swiper-container {{ $isOverflowVisible ? '' : 'overflow-hidden' }} {{ $swiperRequiredClass }}">
        <div class="swiper-wrapper {{ $swiperWrapperClass }}">
            {{ $slot }}
        </div>

        @if (!empty($swiperConfig['pagination']))
            <div class="swiper-pagination {{ $paginationClass }}"></div>
        @endif

        {{-- @if (!empty($swiperConfig['navigation']) && !$isHiddenNavigation)
            <div class="swiper-button-next {{ $nextNavigationClass }}"></div>
            <div class="swiper-button-prev {{ $prevNavigationClass }}"></div>
        @endif --}}
    </div>
</div>

@push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const swiperClass = "{{ $swiperRequiredClass }}";
            const paginationClass = "{{ $paginationClass }}";
            const swiperConfig = {!! json_encode($swiperConfig) !!};

            const initSwiper = () => {
                 const swiper = new Swiper(`.swiper-container.${swiperClass}`, {
                    ...swiperConfig,
                    on: {
                        imagesReady() {
                            swiper.update();
                        }
                    }
                });

                if (swiper) {
                    switch (swiperClass) {
                        case 'filter-swiper':
                            const slides = swiper.slides;
                            slides.forEach((slide, index) => {
                                if ($(slide).hasClass('slide-actived')) {
                                    swiper.slideTo(index);
                                }
                            });
                            break;
                        case 'overview-games-swiper':
                            window.overviewGamesSwiper = swiper;
                        case 'overview-category-mb-swiper':
                            window.overviewCategoryMbSwiper = swiper;
                            break;
                        case 'swiper-games-mobile':
                        case 'swiper-category-tab-mobile':
                            swiper.autoplay.stop();
                            window.addEventListener("DOMContentLoaded", () => {
                                setTimeout(() => swiper.autoplay.start(), 1000);
                            });
                            swiper.slideTo(1);
                            $(`.js-account-overview-tab`).on('click', () => {
                                swiper.slideTo(0);
                                setTimeout(() => {
                                    swiper.slideTo(1);
                                }, 500);
                            });
                            break;
                        case 'swiper-first-bank':
                        case 'swiper-second-bank':
                            swiper.autoplay.stop();
                            window.addEventListener("DOMContentLoaded", () => {
                                setTimeout(() => swiper.autoplay.start(), 1000);
                            });
                            break;
                    }
                }
            }

            userActivityWrapper(()=>{
                initSwiper();
            })

        });
    </script>
@endpush
