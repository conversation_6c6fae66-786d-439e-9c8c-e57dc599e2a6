@props([
    'label' => '',
    'disabled' => false,
    'class' => '',
    'classInputWrap' => '',
    'inputClassName' => '',
    'isRequire' => false,
    'max' => 10
])

<?php
    $classesWrap =  [
        'input-container flex flex-col items-start gap-2 w-full [&.disabled]:pointer-events-none [&.disabled_input]:text-neutral-400 [&.disabled_.input-field-wrap]:bg-neutral-150 [&.error-validate_.input-field-wrap]:border-danger-500',
        'pointer-events-none' => $disabled,
        $class,
    ];

    $classesInputWrap =  [
        'input-field-wrap grid grid-cols-[auto_68px] items-center gap-[8px] h-[40px] pl-[16px] pr-0 border bg-neutral border-neutral-150 rounded-[8px]',
        '[&_input]:text-neutral-600 bg-neutral-150' => $disabled,
        $classInputWrap
    ];

    $classButton = 'flex h-[40px] items-center min-w-[18px] px-[8px] h-[18px] pointer-events-none [&_i]:before:text-[#CFD1D9] [&.active_i]:before:text-[#979CA7] [&.active]:pointer-events-auto';

    $classNameInput = 'input-field w-full pt-[2px] text-neutral-1000 text-[14px] leading-[20px] font-normal bg-transparent outline-0 placeholder:text-neutral-400';
    $classNameInput = twMerge($classNameInput, $inputClassName);
    $classNameWrap = twMerge(flatten_classes($classesWrap));
    $classNameInputWrap = twMerge(flatten_classes($classesInputWrap));
?>

<div class="{{ $classNameWrap }}" type="number" max="{{ $max }}">
    <div class="flex flex-col gap-1 w-full">
        @if ($label)
            <p class="text-[12px] leading-[18px] font-normal text-neutral-1000">
                {{ $label }}
            </p>
        @endif
        <div class="{{ $classNameInputWrap }}">
            
            <input readonly value="1" input-type="number" field-type="input" type="number" {{ $attributes }} class="{{ $classNameInput }} text-left pointer-events-none" />
            
            <div class="flex items-center">
                <button type="button" class="input-number-button input-button-minus {{ $classButton }}">
                    <i class="icon-minus text-[18px] text-neutral-300 before:content-['\e99e']"></i>
                </button>
                
                <button type="button" class="input-number-button input-button-plus {{ $classButton }} active">
                    <i class="icon-plus text-[18px] font-bold text-neutral-300 before:content-['\e965']"></i>
                </button>
            </div>
        </div>
    </div>
    @if ($isRequire)
        <span class="input-error text-[12px] hidden leading-[18px] font-normal text-danger-600"></span>
    @endif
</div>

@pushOnce('scripts')
    @vite('resources/js/input.js')
    
    <script>
        window.addEventListener('DOMContentLoaded', () => {
            $('.input-container').each(function () {
                const container = $(this);
                const type = container.attr('type');

                if (typeof type !== 'undefined' && type !== false) {
                    const input = container.find('.input-field');
                    const buttonMinus = container.find('.input-button-minus');
                    const buttonPlus = container.find('.input-button-plus');
                    const max = Number(container.attr('max'));

                    buttonMinus.on('click', function (event) {
                        event.preventDefault();
                        event.stopPropagation();
                        const value = Number(input.val());
                        const newValue = value - 1;
                        
                        if (newValue >= 1) {
                            if (newValue === 1) {
                                buttonMinus.removeClass('active');
                            } else {
                                buttonMinus.addClass('active');
                            }

                            buttonPlus.addClass('active');

                            input.val(newValue);
                        }
                    });

                    buttonPlus.on('click', function (event) {
                        event.preventDefault();
                        event.stopPropagation();
                        const value = Number(input.val());
                        const newValue = value + 1;
                        
                        if (newValue <= max) {
                            if (newValue === max) {
                                buttonPlus.removeClass('active');
                            } else {
                                buttonPlus.addClass('active');
                            }

                            buttonMinus.addClass('active');
                            input.val(newValue);
                        }
                    })
                }
            })
        })
    </script>
@endpushOnce
