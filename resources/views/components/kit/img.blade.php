@props([
    'src' => '',
    'alt' => '',
    'errorSrc' => '/asset/images/default-image.avif',
    'className' => '',
    'useModernFormats' => true
])

@php
use App\Helpers\ImageHelper;

if ($useModernFormats) {
    $sources = ImageHelper::getOptimizedImageSources($src, $errorSrc);
} else {
    $sources = [['srcset' => $src, 'type' => 'image/jpeg']];
}
@endphp

<div class="image-container">
    @if($useModernFormats && count($sources) > 1)
        <picture>
            @foreach(array_slice($sources, 0, -1) as $source)
                <source srcset="{{ $source['srcset'] }}" type="{{ $source['type'] }}">
            @endforeach

            @php $fallbackSource = end($sources); @endphp
            <img
                src="{{ $fallbackSource['srcset'] }}"
                alt="{{ $alt }}"
                data-error="{{ $errorSrc }}"
                class="lazy-image {{ $className }}"
                loading="lazy"
                decoding="async"
                onerror="this.onerror=null; this.src='{{ $errorSrc }}';"
            />
        </picture>
    @else
        <img
            src="{{ $src }}"
            alt="{{ $alt }}"
            data-error="{{ $errorSrc }}"
            class="lazy-image {{ $className }}"
            loading="lazy"
            decoding="async"
            onerror="this.onerror=null; this.src='{{ $errorSrc }}';"
        />
    @endif
    <div class="skeleton"></div>
</div>

@pushOnce('scripts')
    @vite(['resources/js/optimized-image.js'])
@endpushOnce
