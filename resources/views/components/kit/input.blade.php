@props([
    'type' => 'text',
    'label' => '',
    'disabled' => false,
    'isRequire' => false,
    'class' => '',
    'rightIcon' => null,
    'leftIcon' => null,
    'rightText' => null,
    'isShowIconPass' => false,
    'classInputWrap' => '',
    'inputClassName' => '',
    'isPaste' => false,
    'noBackground' => false,
    'inPopup' => false,
    'isPasteNumber' => false,
    'isPasteWithoutCharacter' => false,
    'rightIconClass' => '',
    'allowEmoji'=>true,
])

<?php
$inputType = $type;
if ($type === 'password' && request()->has('show_password')) {
    $inputType = 'text';
}

    $classesWrap = ['input-container relative flex flex-col gap-[4px] w-full [&.disabled]:pointer-events-none [&.disabled_input]:text-neutral-400 [&.disabled_.input-field-wrap]:bg-neutral-100 [&.error-validate_.input-field-wrap]:border-danger-500', 'pointer-events-none' => $disabled, $class];
    $classesInputWrap = [
        'input-field-wrap flex justify-between items-center gap-[8px] h-[40px] py-[8px] px-[12px] border border-transparent bg-neutral-50 rounded-[8px]', 
        '[&_input]:text-neutral-600 bg-neutral-100' => $disabled, $classInputWrap,
        'bg-neutral border-neutral-150' => $noBackground
    ];

    $classNameInput = 'input-field w-full pt-[2px] text-neutral-1000 text-[14px] leading-[20px] font-normal bg-transparent outline-0 placeholder:text-sm placeholder:text-neutral-600 placeholder:!normal-case';
    $classNameInput = twMerge($classNameInput, $inputClassName);
    $classNameWrap = twMerge(flatten_classes($classesWrap));
    $classNameInputWrap = twMerge(flatten_classes($classesInputWrap));
?>

<div class="{{ $classNameWrap }}">
    <label class="flex flex-col gap-[4px] w-full">
        @if ($label)
            <p class="text-[12px] leading-[18px] font-normal text-neutral-1000">
                {{ $label }}
            </p>
        @endif
        <div class="{{ $classNameInputWrap }}">
            @if ($leftIcon)
                <i class="text-[14px] text-neutral-600 cursor-pointer {{ $leftIcon }}"></i>
            @endif

            <div class="flex flex-grow h-full">
                <input 
                    input-type="text" 
                    field-type="input" 
                    @if ($isPasteNumber) isPasteNumber="true" @endif  
                    @if ($allowEmoji) allowEmoji= "true" @endif
                    @if ($isPasteWithoutCharacter) isPasteWithoutCharacter="true" @endif 
                    type="{{ $inputType }}" 
                    {{ $attributes }} 
                    class="{{ $classNameInput }}" 
                    {{$disabled?'disabled':''}}
                />
            </div>

            @if ($type === 'password' && $isShowIconPass)
                <i class="input-icon-password icon-eye-slash text-[18px] text-neutral-600 cursor-pointer"></i>
            @endif

            @if ($rightIcon)
                <i class="text-[18px] text-neutral-600 cursor-pointer {{ $rightIcon }} {{ $rightIconClass }}"></i>
            @endif
            @if ($rightText)
                <span
                    class="input-right-text text-neutral-1000 text-[12px] leading-[18px] font-normal italic">{{ $rightText }}</span>
            @endif
            @if ($isPaste)
                <x-kit.button button-type="button" class="input-button-paste w-full max-w-max rounded-full" size="small" type="ghost">Dán</x-kit.button>
            @endif
        </div>
    </label>
    @if ($isRequire)
        <span class="input-error {{ $inPopup ? 'absolute top-[calc(100%+3px)] left-0' : '' }} text-[12px] hidden leading-[18px] font-normal text-danger-600"></span>
    @endif
</div>

@pushOnce('scripts')
    @vite('resources/js/input.js')
@endPushOnce
