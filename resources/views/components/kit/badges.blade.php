@props([
    'number' => null,
])

<div class="relative">
    @if (!empty($number))
            <div class="absolute bottom-[20px] left-[8px] z-[1] flex items-center py-[1.5px] px-[5.5px] bg-danger-500 border border-neutral rounded-xl xl:bottom-[32px] xl:left-[20px]">
                <p class="text-[8px] leading-[12px] font-medium text-neutral">{{ $number }}+</p>
        </div>
    @endif
    <x-kit.button onclick="window.location.href='/notification'" style="filled" type="tertiary" aria-label="notification" size="small" class="flex w-[28px] h-[28px] xl:hidden relative text-neutral-1000 bg-neutral-150 rounded-full" symbol="icon-notification"></x-kit.button>
    <x-kit.button style="filled" type="tertiary" aria-label="notification" size="large" class="js-notification hidden xl:flex relative text-neutral-1000 bg-neutral-150" symbol="icon-notification">
    </x-kit.button>
    <div class="total-notification absolute hidden right-[-8px] top-[-8px] w-[28px] h-[16px] items-center justify-center rounded-[100px] font-medium text-[8px] leading-[calc(12/8)] text-neutral border border-solid border-neutral bg-danger-500"></div>
    
    <div class="hidden xl:block">
        <div id="notification-dropdown" class="notification hidden absolute left-2/4 top-[calc(100%+8px)] -translate-x-[75%] pl-[16px] pr-[7px] py-[15px] bg-neutral rounded-lg border border-solid border-neutral-150 z-[60] w-[25.25rem]">
            <div class="flex flex-col gap-3">
                <div class="flex items-center justify-between h-6 pr-2">
                    <div class="uppercase text-xs leading-[calc(18/12)] font-medium text-neutral-1000">
                        Thông báo
                    </div>
                </div>
               <div class="xl:h-[18.3rem] h-[35rem] flex items-center justify-center flex-col pr-2">
                    <img src="{{ asset('/asset/images/notification/empty.svg') }}" class="w-[68px] h-[68px] xl:w-[100px] xl:h-[100px]" alt="empty" />
                    <p class="mt-4 text-neutral-800 text-[12px] leading-[calc(18/12)] xl:text-sm xl:leading-[calc(20/14)]">Không có thông báo nào.</p>
                </div>
            </div>    
        </div>
    </div>
</div>
@pushOnce('scripts')
    @vite('resources/js/notification.js')
    @vite('resources/js/notification-mb.js')
@endPushOnce
