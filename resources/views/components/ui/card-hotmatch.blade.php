@props(['hotMatch'])
@use(App\Enums\GatewayEndpoint)
@use(App\Enums\UrlPathEnum)
<div
    onclick="openSport({
        link: '{{ UrlPathEnum::K_SPORTS }}' + '?leagueId={{ $hotMatch->league_id }}&matchId={{ $hotMatch->match_id }}',
        apiUrl: '{{ GatewayEndpoint::K_SPORTS }}',
        loginRequired: '{{ $hotMatch->loginRequired ?? false }}',
        params: {
            leagueId: '{{ $hotMatch->league_id }}',
            matchId: '{{ $hotMatch->match_id }}',
        }
    })"
    class="rounded-xl cursor-pointer w-full relative bg-neutral p-[12px] pb-[10px] min-h-[162px] max-w-[301px]">
    <div class="flex justify-between items-center gap-2 mb-[10px]">
        <div class="flex gap-1 items-center max-w-[210px]">
            <span class="text-[10px] leading-[14px] text-neutral-1000 truncate">{{ $hotMatch->league_name_text }}</span>
        </div>
        <div class="flex justify-between items-center">
            <div class="flex gap-1 text-[10px] leading-[14px]">
                <span class="text-neutral-1000 match-time-{{ $hotMatch->match_id }}"></span>
                <span class="text-neutral-1000 match-date-{{ $hotMatch->match_id }}"></span>
            </div>
        </div>
    </div>
    <div>
        <div class="grid grid-cols-[1fr_32px_1fr] items-center gap-[8px] mb-[18px]">
            <div class="grid grid-cols-[1fr_26px] items-center gap-1.5 xl:grid-cols-[1fr_32px] xl:gap-2">
                <span class="max-w-full text-[10px] xl:text-[12px] text-right text-neutral-1200 truncate">{{ $hotMatch->teams[0]->name ?? '-' }}</span>
                <img src="{{ $hotMatch->teams[0]->flag_thumbnail }}" alt="hotmatch"
                    class="size-[16px] xl:size-[32px] object-contain" loading="lazy" fetchpriority="low"
                    onerror="this.onerror=null; this.src='{{ asset('asset/images/default-sport.avif') }}'"
                />
            </div>
            <div class="text-[18px] font-bold text-center text-neutral-1000">VS</div>
            <div class="grid grid-cols-[26px_1fr] items-center gap-1.5 xl:grid-cols-[32px_1fr] xl:gap-2">
                <img src="{{ $hotMatch->teams[1]->flag_thumbnail }}" alt="hotmatch"
                    class="size-[16px] xl:size-[32px] object-contain" loading="lazy" fetchpriority="low"
                    onerror="this.onerror=null; this.src='{{ asset('asset/images/default-sport.avif') }}'"
                />
                <span
                    class="max-w-full text-[10px] xl:text-[12px] text-neutral-1200 truncate">{{ $hotMatch->teams[1]->name ?? '-' }}</span>
            </div>
        </div>
        <div class="grid grid-cols-2 gap-y-1 xl:gap-x-[9px] gap-x-[4.5px] min-w-[120px] xs:min-w-[150px] xl:min-w-[176px]">
            <div
                class="p-[3.5px] xl:p-[5.5px] xl:px-[8px] bg-neutral-50 rounded xl:text-xs text-[10px] flex xl:gap-[7px] gap-[9px] justify-center items-center w-full leading-[14px] xl:leading-[18px] font-medium">
                <div class="w-1/3 text-neutral-800 font-bold">H</div>
                <span class="w-1/3 text-center text-neutral-1000">{{ isset($hotMatch->hdp->hTeam->rate) ? $hotMatch->hdp->hTeam->rate : '-' }}</span>
                <span
                    class="@if (isset($hotMatch->hdp->hTeam->odds) && floatval($hotMatch->hdp->hTeam->odds) < 0) text-danger-600 @else text-success-600 @endif w-1/3 text-right">{{ $hotMatch->hdp->hTeam->odds ?? '-' }}</span>
            </div>
            <div
                class="p-[3.5px] xl:p-[5.5px] xl:px-[8px] bg-neutral-50 rounded xl:text-xs text-[10px] flex xl:gap-[7px] gap-[9px] justify-center items-center w-full leading-[14px] xl:leading-[18px] font-medium">
                <div class="w-1/3 text-neutral-800 font-bold">O</div>
                <span class="w-1/3 text-center text-neutral-1000">{{ $hotMatch->ou->hTeam->rate ?? '-' }}</span>
                <span
                    class="@if (isset($hotMatch->ou->hTeam->odds) && floatval($hotMatch->ou->hTeam->odds) < 0) text-danger-600 @else text-success-600 @endif w-1/3 text-right">{{ $hotMatch->ou->hTeam->odds ?? '-' }}</span>
            </div>
            <div
                class="p-[3.5px] xl:p-[5.5px] xl:px-[8px] bg-neutral-50 rounded xl:text-xs text-[10px] flex xl:gap-[7px] gap-[9px] justify-center items-center w-full leading-[14px] xl:leading-[18px] font-medium">
                <div class="w-1/3 text-neutral-800 font-bold">A</div>
                <span class="w-1/3 text-center text-neutral-1000">{{ $hotMatch->hdp->aTeam->rate ?? '-' }}</span>
                <span
                    class="@if (isset($hotMatch->hdp->aTeam->odds) && floatval($hotMatch->hdp->aTeam->odds) < 0) text-danger-600 @else text-success-600 @endif w-1/3 text-right">{{ $hotMatch->hdp->aTeam->odds ?? '-' }}</span>
            </div>
            <div
                class="p-[3.5px] xl:p-[5.5px] xl:px-[8px] bg-neutral-50 rounded xl:text-xs text-[10px] flex xl:gap-[7px] gap-[9px] justify-center items-center w-full leading-[14px] xl:leading-[18px] font-medium">
                <div class="w-1/3 text-neutral-800 font-bold">U</div>
                <span class="w-1/3 text-center text-neutral-1000">{{ $hotMatch->ou->aTeam->rate ?? '-' }}</span>
                <span
                    class="@if (isset($hotMatch->ou->hTeam->odds) && floatval($hotMatch->ou->aTeam->odds) < 0) text-danger-600 @else text-success-600 @endif w-1/3 text-right">{{ $hotMatch->ou->aTeam->odds ?? '-' }}</span>
            </div>
        </div>

    </div>
</div>

<script>
    (function() {
        const dateObj = new Date('{{ $hotMatch->text_time }}');

        const time = dateObj.toLocaleTimeString('en-US', {
            hour12: false,
            hour: '2-digit',
            minute: '2-digit'
        });
        const date_str = dateObj.toLocaleDateString('en-GB', {
            day: '2-digit',
            month: '2-digit'
        });
        const times = document.querySelectorAll('.match-time-{{ $hotMatch->match_id }}');
        const dates = document.querySelectorAll('.match-date-{{ $hotMatch->match_id }}');

        times.forEach(item => item.innerHTML = time);
        dates.forEach(item => item.innerHTML = date_str);
    })();
</script>