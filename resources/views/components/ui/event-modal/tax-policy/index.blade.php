@php
    $id="tax-policy-modal";
    $brand = config('app.brand_name');
@endphp

<div id="{{$id}}" class="js-event-modal fixed inset-0 z-[1001] flex justify-center items-center px-[10px] pt-[7px] bg-black bg-opacity-70 border-box overflow-auto xl:pt-[1px]">
    <div id="{{ $id }}-container" class="relative flex flex-col items-center w-full max-w-[366px] xl:max-w-[440px]">
        <div class="relative w-full h-full bg-neutral rounded-[16px] px-[16px] py-[20px] xl:px-[32px] xl:py-[32px]">

            <button class="{{$id}}-close z-[1002] absolute top-[10px] right-[10px] flex items-center justify-center w-[32px] h-[32px] text-xl bg-neutral-100 hover:bg-neutral-200 rounded-full sm:flex">
                <img alt="close" src="{{ asset('asset/images/close-gold.avif') }}" class="w-[8px] aspect-square"/>
            </button>

            <div class="flex flex-col items-center xl:gap-2">
                <img 
                    src={{ asset('/asset/images/event/tax-policy/icon-paper.avif') }} 
                    class="w-[96px] aspect-square mb-[16px] xl:mb-[8px]" 
                    alt="tax policy"
                />

                <span class="text-neutral-1000 text-[18px] font-semibold mb-2 xl:mb-0">THÔNG BÁO VỀ CHÍNH SÁCH THUẾ</span>

                <p class="text-sm text-neutral-850 text-center">{{$brand}} không thuộc nhóm dịch vụ thương mại hay kinh doanh sản phẩm, nên
                    <span class="font-semibold">không phát sinh trách nhiệm thuế đối với người dùng</span> 
                    dưới bất kỳ hình thức nào.
                </p>

                <p class="text-sm text-neutral-850 text-center">Chúng tôi cam kết:
                    <span class="font-semibold">Hệ thống bảo mật cao, bảo vệ toàn diện</span>
                    thông tin cá nhân và tài khoản của quý khách.
                </p>

                <p class="text-sm text-neutral-850 text-center">Giao dịch nạp và rút tiền luôn được
                    <span class="font-semibold">hỗ trợ 24/7</span>, đảm bảo sự
                    <span class="font-semibold">nhanh chóng, an toàn và minh bạch.</span>
                </p>

                <label class="z-[1002] flex justify-center items-center gap-2 w-max cursor-pointer mt-[20px] xl:mt-[16px]">
                    <input type="checkbox" hidden class="{{$id}}-show peer">
                    <img 
                        class="block size-5 peer-checked:hidden" 
                        src="{{ asset('/asset/images/event/tax-policy/checkbox.svg') }}"
                    />
                    <img 
                        class="hidden size-5 peer-checked:block" 
                        src="{{ asset('/asset/images/event/tax-policy/checkbox-active.svg') }}"
                    />
                    <p class="text-sm text-neutral-850">Không hiển thị lại</p>
                </label>
            </div>
        </div>

    </div>
</div>
