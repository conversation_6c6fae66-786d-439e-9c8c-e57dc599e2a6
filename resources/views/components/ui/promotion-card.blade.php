@props(['promotion' => [],'isPagePromotion' => false , 'isOverview' => false])

@if (isset($promotion['type']))
    <div class="swiper-slide">
        <a class="swiper-slide" href="{{ $promotion['link'] }}" aria-label="promotion link {{ $promotion['name'] }}">
            <div class="promotion-card relative overflow-hidden rounded-lg xl:aspect-[404/180] {{ $isPagePromotion ? 'xl:max-w-[400px]' : '' }}">
                @if($isPagePromotion)
                <div class="block xl:hidden">
                    <img src="{{ asset($promotion['imgmbAcc']) }}" class="sm:w-full object-contain xl:w-full"
                        loading="lazy" alt="mobile promotion image {{ $promotion['name'] }}" />
                </div>
                <div class="hidden w-full xl:block thumbnail-item">
                    <img
                        src="{{ asset($promotion['imgAcc']) }}"
                        class="w-full duration-300 transition-all hover:scale-105"
                        loading="lazy"
                        alt="desktop promotion image {{ $promotion['name'] }}"
                    />
                </div>
                <x-kit.button
                    aria-label="see promotion details {{ $promotion['name'] }}"
                    class="hidden xl:flex xl:gap-1 justify-center items-center absolute bottom-0 left-0 w-[98px] h-[28px] py-0 px-[10px] xl:h-[32px] text-[10px] leading-[14px] xl:w-[159px] bg-primary-500 rounded-none rounded-tr-[10px] xl:rounded-tr-[24px] text-neutral hover:bg-primary-600 hover:text-neutral">
                    <span class="text-nowrap text-sm font-medium capitalize">{{ __('common.see_detail') }}</span>
                </x-kit.button>

                @else
                <div class="block xl:hidden">
                    <img src="{{$isOverview ? asset($promotion['imgmbAcc']) : asset($promotion['imgmb']) }}" class="sm:w-full object-contain xl:w-full"
                        loading="lazy" alt="mobile promotion image {{ $promotion['name'] }}" />
                </div>
                <div class="hidden w-full xl:block thumbnail-item">
                    <img
                        src="{{ asset($promotion['img']) }}"
                        class="w-full duration-300 transition-all hover:scale-105"
                        loading="lazy"
                        alt="desktop promotion image {{ $promotion['name'] }}"
                    />
                </div>
                {{-- <x-kit.button
                    aria-label="see promotion details {{ $promotion['name'] }}"
                    class="flex gap-1 justify-center items-center absolute bottom-0 right-0 w-[98px] h-[28px] py-0 px-[10px] xl:h-[36px] text-[10px] xl:text-xs xl:leading-[18px] leading-[14px] xl:w-[139px] bg-secondary-500 rounded-none rounded-tl-[10px] xl:rounded-tl-[24px] text-neutral hover:bg-secondary-600 hover:text-neutral">
                    <span class="text-nowrap capitalize">{{ __('common.see_more') }}</span>
                </x-kit.button> --}}
                @endif
            </div>
        </a>
    </div>

@endif