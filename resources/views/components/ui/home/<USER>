@props(['nearWin'])
<div class="relative z-[10] overflow-hidden container -mt-[112px] max-[1800px]:-mt-[110px] max-[1400px]:-mt-[60px]">
    <div class="flex justify-between items-center mb-2 xl:mb-[21.5px]">
        <x-ui.title-section title="Top" titleHighlight="Jackpot" titleButton=""></x-ui.title-section>
        <div class="xl:flex gap-2 hidden">
            <button aria-label="prev" class="topjackpot-nav text-primary-600 w-8 h-8 text-[16px] flex justify-center items-center topjackpot-prev bg-neutral-100 rounded-full [&.swiper-button-disabled]:bg-neutral-50 [&.swiper-button-disabled>i::before]:text-neutral-300">
                <i class="icon-arrow-left-fill"></i>
            </button>
            <button aria-label="next" class="topjackpot-nav text-primary-600 w-8 h-8 text-[16px] flex justify-center items-center topjackpot-next bg-neutral-100 rounded-full [&.swiper-button-disabled]:bg-neutral-50 [&.swiper-button-disabled>i::before]:text-neutral-300">
                <i class="icon-arrow-right-fill"></i>
            </button>
        </div>
    </div>
    <x-ui.home.swiper-top-winner-home :nearWin="$nearWin" />
</div>
