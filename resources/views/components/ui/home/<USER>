@props(['hotMatches', 'swiperRequiredClass'])
@php
    $swiperRequiredClass = $swiperRequiredClass ?? 'hot-match-swiper';
@endphp
<?php
$sports = config('home.sports') ?? [];
$hotMatchSwiper = config('sports.hotMatchSwiper') ?? [];
?>
@if (!empty($hotMatches))
<div class="max-w-full overflow-hidden relative hidden xl:block bg-sports-gradient py-[20px]">
    <div class="container flex justify-between items-center h-[46px] mb-2 xl:mb-4">
        <x-ui.title-section title="Cuồng nhiệt" titleHighlight="sports" titleButton=""></x-ui.title-section>
        <div class="xl:flex gap-2 hidden">
            <button aria-label="prev" class="sports-nav text-primary-600 w-8 h-8 text-[16px] flex justify-center items-center sports-prev bg-neutral-100 rounded-full [&.swiper-button-disabled]:bg-neutral-50 [&.swiper-button-disabled>i::before]:text-neutral-300">
                <i class="icon-arrow-left-fill"></i>
            </button>
            <button aria-label="next" class="sports-nav text-primary-600 w-8 h-8 text-[16px] flex justify-center items-center sports-next bg-neutral-100 rounded-full [&.swiper-button-disabled]:bg-neutral-50 [&.swiper-button-disabled>i::before]:text-neutral-300">
                <i class="icon-arrow-right-fill"></i>
            </button>
        </div>
    </div>
    <div class="w-full flex justify-center hotmatch">
        <div class="hotmatch-wrapper container z-10">
            @if (!empty($hotMatches))
                <x-kit.swiper :swiperConfig="$hotMatchSwiper" swiperWrapperClass="!w-full relative" :swiperRequiredClass="$swiperRequiredClass" id="sport">
                    <!-- Slides -->
                    @foreach ($hotMatches as $hotMatch)
                        <div class="swiper-slide xl:max-w-[301px] xl:mr-[12px]" onclick="event.stopPropagation()">
                            <x-ui.card-hotmatch :hot-match="$hotMatch" />
                        </div>
                    @endforeach
                </x-kit.swiper>
            @endif
        </div>
    </div>
 </div>
@endif
