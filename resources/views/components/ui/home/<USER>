@php
    $validEvent = request()->get('validRewardEvent');
    $beforeEvent = request()->get('beforeRewardEvent');
    $startEventDay = env('START_REWARD_EVENT_DAY');
    $endEventDay = env('END_REWARD_EVENT_DAY');
@endphp

<a href="/san-thuong-gio-vang" aria-label="hero-banner" class="block h-full w-full cursor-pointer">
    <div class="relative h-full xl:container">
        <img 
            src="{{ asset('/asset/images/home/<USER>/reward-event/reward-event-bg.avif') }}"
            class="absolute top-0 left-0 z-0 block w-full h-full xl:hidden"
            alt="reward"
        />
        <div class="relative z-[1] flex justify-between items-center gap-[19px] h-full pl-[5.95%] pr-[1.22%] xl:hidden">
            <div class="relative flex flex-col w-[43.96%] h-full pt-1">
                @if ($beforeEvent || $validEvent) 
                    <div class="relative flex items-center w-[120px] h-4 pt-[2px] pl-[16px]">
                        <img  alt="reward" src="{{ asset('/asset/images/home/<USER>/reward-event/reward-event-day-mb.avif') }}" class="absolute top-0 left-0 z-0 w-full h-full"/>
                        <p class="relative z-[1] text-[8px] leading-[8px] text-neutral-1000 font-semibold italic tracking-[-0.3px]">{{ $startEventDay }} → {{ $endEventDay }}</p>
                    </div>
                @else
                    <img alt="reward" src="{{ asset('/asset/images/home/<USER>/reward-event/reward-event-end-mb.avif') }}" class="w-[101px] h-4 ml-[3px]"/>
                @endif
                <img 
                    alt="reward"
                    src="{{ asset('/asset/images/home/<USER>/reward-event/reward-event-content-mb.avif') }}"
                    class="w-full aspect-[151/73]"
                />
            </div>
            <img 
                alt="reward"
                src="{{ asset('/asset/images/home/<USER>/reward-event/reward-event-mb.avif') }}"
                class="w-[46.55%] h-full"
            />
        </div>
        <div class="relative z-[1] hidden justify-start items-start h-full xl:flex">
            <div class="relative w-[42.01%] mt-[60px]">
                @if ($beforeEvent || $validEvent) 
                    <div class="absolute bottom-[calc(100%+7px)] left-[5px] flex items-center w-[269px] h-[37px] pt-[5px] pl-[38px]">
                        <img alt="reward" src="{{ asset('/asset/images/home/<USER>/reward-event/reward-event-day-pc.avif') }}" class="absolute top-0 left-0 z-0 w-full h-full"/>
                        <p class="relative z-[1] text-[16px] leading-[16px] text-neutral-1000 font-semibold italic">{{ $startEventDay }} → {{ $endEventDay }}</p>
                    </div>
                @else
                    <img alt="reward" src="{{ asset('/asset/images/home/<USER>/reward-event/reward-event-end-pc.avif') }}" class="absolute bottom-[calc(100%+16px)] left-[5px] w-[174px] h-7"/>
                @endif
                <img 
                    src="{{ asset('/asset/images/home/<USER>/reward-event/reward-event-content-pc.avif') }}"
                    class="w-full aspect-[521/246]"
                    alt="reward"
                />
            </div>
            <img 
                src="{{ asset('/asset/images/home/<USER>/reward-event/reward-event-pc.avif') }}"
                class="absolute bottom-0 right-0 z-0 w-[59.75%] aspect-[741/522]"
                alt="reward"
            />
        </div>
    </div>
</a>
