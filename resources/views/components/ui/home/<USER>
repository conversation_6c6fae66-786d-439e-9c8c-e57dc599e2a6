@if ((isset ($gameList) && $gameList && count($gameList) > 0) || (isset ($newGameList) && $newGameList && count($newGameList) > 0))
    @php
        $isMobile =  App\Helpers\DetectDeviceHelper::isMobile();
        $swiperConfig= [
            'slidesPerView' => 2.45,
            'spaceBetween' => 6,
            'breakpoints' => [
                '1200' => [
                    'slidesPerView' => 3.1812,
                    'spaceBetween' => 12,
                ],
            ],
            'speed' => 800,
            'loop' => true,
            'autoplay' => [
                'delay' => 3500,
                'disableOnInteraction' => false,
            ],
        ];
        $swiperClassPC = 'livestreamlist__content-hot-swiper';
        $swiperClassPCNew = 'livestreamlist__content-new-swiper';
        if (!isset($type)) {
            $type = 'home';
        }

        $showJackpotPromoList = [
            [
                "id" => "bc_77784",
                "url" => asset('/asset/images/home/<USER>/jackpot-promo-30.avif'),
                "class" => "w-[90px]"
            ],
            [
                "id" => "bacca_77778",
                "url" => asset('/asset/images/home/<USER>/jackpot-promo-13.avif'),
                "class" => "w-[90px]"
            ],
            [
                "id" => "xd_77786",
                "url" => asset('/asset/images/home/<USER>/jackpot-promo-100.avif'),
                "class" => "w-[96px]"
            ],
            [
                "id" => "sb_77783",
                "url" => asset('/asset/images/home/<USER>/jackpot-promo-999.avif'),
                "class" => "w-[99px]"
            ],
        ];

        $promoURL = null;
        $promoClass = '';
        $highlightGame = null;
        $games = [];
        $jackpot = 0;

        if (count($gameList) > 0) {
            foreach ($gameList as $index => $gameItem) {
                if ( $index == 0 ) {
                    $highlightGame = $gameItem;
                } else {
                    array_push($games, $gameItem);
                }
            }

            if (!$highlightGame) {
                $highlightGame = $gameList[0] ?? null;
                $games = array_slice($gameList, 1, 6) ?? [];
            }
        }

        $partnerGameJacpot = collect([
            'rik_vgmn_108',
            'rik_vgmn_109',
            'rik_vgmn_110',
            'rik_vgmn_111',
            'go_qs_txgo-101',
            'go_qs_xocdia-102',
            'go_vgmn_109',
            'go_vgmn_100',
            'go_vgmn_221',
            'b52_vgmn_108',
            'b52_vgmn_109',
            'b52_vgmn_110',
            '789club_G1X_305',
            '789club_G1X_306',
            'sunwin_G1S_305',
            'sunwin_G1X_306',
            'sun_g1s_305',
            'sun_g1s_306',
        ]);

        if ($highlightGame) {
            foreach ($showJackpotPromoList as $item) {
                if ($item['id'] === $highlightGame->partner_game_id) {
                    $promoURL = $item['url'];
                    $promoClass = $item['class'];
                    break;
                }
            }

            $jackpot = get_jackpot_by_game_id( $partnerGameJacpot->contains(($highlightGame->partner ?? '') . '_' . $highlightGame->partner_game_id) ? (($highlightGame->partner ?? '') . '_' . $highlightGame->partner_game_id) : $highlightGame->partner_game_id);
        }
    @endphp

    @if ($isMobile)
        @pushOnce('preloadLink')
            <link rel="preload" href="{{ asset('/asset/images/home/<USER>/img-preview-mb-xd_77786.avif')  }}" as="image" type="image/avif">
        @endPushOnce
    @endif

    <div class="bg-neutral xl:bg-neutral-50 xl:py-[60px] {{ isset($type) && $type === 'lobby' ? 'pb-[12px]' : 'pb-[16px]' }}">
        @if (!isset($type) || $type !== 'lobby')
            <div class="flex justify-between items-center relative container">
                <h2 class="font-semibold leading-[26px] mr-auto text-[18px] text-neutral-950 capitalize xl:leading-[43px] xl:text-[36px]">Live <span class="text-secondary-400">Casino</span></h2>
                <a class="cursor-pointer font-medium xl:hover:text-neutral-1000 leading-[18px] ml-auto text-[12px] text-neutral-800 xl:leading-[20px] xl:text-[14px] flex items-center gap-[4px] xl:gap-[6px]"
                        href="/song-bai-livecasino-truc-tuyen">
                        {{ __('common.see_more') }}
                        <img src="{{ asset('asset/icons/arrow-right.svg') }}" alt="see more" class="size-[14px] xl:size-[20px]">
                </a>
            </div>
        @endif
        <div class="flex flex-col xl:flex-row xl:gap-5 gap-[8px] mt-2 xl:mt-5 xl:max-h-[372px] container !pr-0 xl:!pr-[0.625rem]">
            <div class="live-highlight-container relative xl:w-[555px] w-full xl:pr-0">
                {{-- quickbet --}}
                <div class="quickbet-container item-image relative h-full flex-shrink-0 cursor-pointer overflow-hidden max-lg:aspect-[400/270] max-lg:rounded-lg lg:aspect-[400/270] lg:rounded-2xl">
                    <picture>
                        <source 
                            srcset="{{ asset('/asset/images/home/<USER>/img-preview-xd_77786.avif' ) }}" 
                            type="image/avif"
                            media="(min-width: 1200px)"
                        >
                        <img 
                            class="h-full" 
                            src="{{ asset('/asset/images/home/<USER>/img-preview-mb-xd_77786.avif' )  }}" 
                            alt="stream preview"
                            fetchpriority="{{ $isMobile ? 'high' : 'low'}}"
                            loading="{{ $isMobile ? 'eager' : 'lazy'}}">
                    </picture>
                    <canvas class="quickbet-canvas {{ Auth::check() ? "" : "pointer-events-none" }}" id="pixi-widget-xocdia-77786" style="touch-action: none; cursor: inherit;"></canvas>
                </div>
            </div>
            <div class="xl:w-[calc(100%_-_(555px_+_20px))] w-full">
               <div class="highlights-game flex-col gap-1 hidden xl:flex">
                    <div class="highlights-game__title font-medium leading-[1.2] text-[36px] text-neutral-1200">
                        {{ $highlightGame->name ?? '' }}
                    </div>
                    <div class="flex flex-col gap-2">
                        <div class="highlights-game__viewers text-sm leading-[1.2] text-neutral-1800">
                            <span class="font-semibold js-item-viewers js-{{$highlightGame->partner}}-viewers" data-game-id="{{ $highlightGame->partner_game_id }}" data-partner="{{ $highlightGame->partner }}">{{ $highlightGame->viewers ?? 0 }}</span> Đang chơi
                        </div>
                    </div>
                </div>
                <div class="livestreamlist flex flex-col gap-2 xl:gap-6 xl:mt-6">
                    <div class="livestreamlist__tab flex gap-2">
                        <div
                            class="js-livestreamlist-tab-item livestreamlist__tab-item cursor-pointer active rounded-full pl-2 pr-1 xl:pl-4 xl:pr-[0.625rem] flex items-center justify-between text-xs xl:text-base font-medium text-neutral-1650 w-[78px] h-[32px] xl:h-[44px] xl:w-[112px]"
                            data-tab="live-hot"
                        >
                            Games <span class="livestreamlist__tab-item-count text-sm hidden min-w-[22px] xl:min-w-6 xl:w-6 xl:h-6 w-[22px] h-[22px] rounded-full items-center justify-center bg-count-items ml-[4px] xl:ml-[8px]">{{ count($gameList) - 1 }}</span>
                        </div>
                        <div
                            class="js-livestreamlist-tab-item livestreamlist__tab-item text-nowrap cursor-pointer rounded-full pl-2 pr-1 xl:pl-4 xl:pr-[0.625rem] flex items-center justify-center text-xs xl:text-base font-medium text-neutral-1650 min-w-[78px] h-[32px] xl:h-[44px] xl:min-w-[112px]"
                            data-tab="live-new"
                        >
                            Games Mới <span class="livestreamlist__tab-item-count text-sm hidden min-w-[22px] xl:min-w-6 xl:w-6 xl:h-6 w-[22px] h-[22px] rounded-full items-center justify-center bg-count-items ml-[4px] xl:ml-[8px]">6</span>
                        </div>
                    </div>
                    <div class="livestreamlist__content">
                        <div id="live-hot" class="livestreamlist__content-hot">
                            <x-kit.swiper :swiperRequiredClass="$swiperClassPC" class="overflow-x-auto" id="hotlivestream-swiper" :swiperConfig="$swiperConfig">
                                @foreach ($games as $item)
                                    <div class="swiper-slide mr-[8px] max-w-[40%] xl:max-w-[170px] xl:mr-[12px] [&_.title]:!text-[13.1px] [&_.partner-text]:!text-[9.83px] [&_.js-game-favorite]:!text-[13.1px]">
                                        <x-ui.card
                                            name="{{ $item->name ?? 'title' }}"
                                            image="{{ $item->image ?? '' }}"
                                            :game="$item"
                                            page="home"
                                            type="casino"
                                            data-api="{{ $item->api_url ?? '' }}"
                                            id="{{ $item->partner_game_id ?? '' }}"
                                            provider="{{ $item->partner_txt ?? '' }}"
                                            table_id="{{ $item->table_id ?? '' }}"
                                            partner="{{ $item->partner ?? '' }}"
                                            favorite="{{ isset($item->is_favorite) && $item->is_favorite ? 'favorite' : '' }}"
                                            class="flex flex-col items-center text-marron loader-image-transparent"
                                            tags="{{ $item->tags ?? '' }}"
                                            isSlider
                                            isLiveStream
                                            showWinRate
                                            required_login="{{ $item->required_login ?? false }}"
                                        >
                                        </x-ui.card>
                                    </div>
                                @endforeach
                            </x-kit.swiper>
                        </div>

                        <div id="live-new" class="livestreamlist__content-new opacity-0 visible h-0">
                            <x-kit.swiper :swiperRequiredClass="$swiperClassPCNew" class="overflow-x-auto" id="newlivestream-swiper" :swiperConfig="$swiperConfig">
                                @foreach (($newGameList ?? []) as $newGame)
                                    <div class="swiper-slide mr-[8px] max-w-[40%] xl:max-w-[170px] xl:mr-[12px] [&_.title]:!text-[13.1px] [&_.partner-text]:!text-[9.83px] [&_.js-game-favorite]:!text-[13.1px]">
                                        <x-ui.card
                                            name="{{ $newGame->name ?? 'title' }}"
                                            image="{{ $newGame->image ?? '' }}"
                                            :game="$newGame"
                                            page="home"
                                            type="casino"
                                            data-api="{{ $newGame->api_url ?? '' }}"
                                            id="{{ $newGame->partner_game_id ?? '' }}"
                                            provider="{{ $newGame->partner_txt ?? '' }}"
                                            partner="{{ $newGame->partner ?? '' }}"     
                                            favorite="{{ isset($newGame->is_favorite) && $newGame->is_favorite ? 'favorite' : '' }}"
                                            class="flex flex-col items-center text-marron loader-image-transparent"
                                            tags="{{ $newGame->tags ?? '' }}"
                                            isSlider
                                            isLiveStream
                                            showWinRate
                                            required_login="{{ $newGame->required_login ?? false }}"
                                        >
                                        </x-ui.card>
                                    </div>
                                @endforeach
                            </x-kit.swiper>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @php
        $token = Auth::user()?->tp_token ?? '';
        $liveChatLink =config('app.live_chat_link');
    @endphp
    @pushOnce('scripts')
        <script>
            window.handleRenderQuickbet = (tokenUser = "") => {
                const root = 'https://assets.vgjt.info';
                const games_qb = [
                    // {
                    //     "type": "quickbet",
                    //     "partner_game_id": "bc_77784",
                    //     "name": "Bầu Cua BigWin",
                    //     "partner_provider": "techplay",
                    //     "tag": "pixi-widget-baucua-77784",
                    //     "assetUrl": "https://assets.vgjt.info/images/widgetBaucua"
                    // },
                    {
                        "type": "quickbet",
                        "partner_game_id": "xd_77786",
                        "name": "Xóc dĩa BigWin",
                        "partner_provider": "techplay",
                        "tag": "pixi-widget-xocdia-77786",
                        "assetUrl": "https://assets.vgjt.info/images/widgetXocdia"
                    },
                    // {
                    //     "type": "quickbet",
                    //     "partner_game_id": "sb_77783",
                    //     "name": "Sicbo BigWin",
                    //     "partner_provider": "techplay",
                    //     "tag": "pixi-widget-sicbo-77783",
                    //     "assetUrl": "https://assets.vgjt.info/images/widgetSiccbo"
                    // }
                ];

                const scriptId = 'quickbet-script';
                const scriptList = document.querySelectorAll("script[type='text/javascript']");
                const convertedNodeList = Array.from(scriptList);
                const testScript = convertedNodeList.find(script => script.id === scriptId);
                
                if (testScript !== undefined) {
                    testScript.parentNode.removeChild(testScript);
                }

                games_qb.filter(game => game.type === "quickbet")
                    .forEach(game => {
                        const { tag, assetUrl } = game;
                        const script = document.createElement('script');

                        script.type = 'module';
                        script.id = scriptId;
                        script.src = `${root}/js/${tag}.js`;
                        script.onload = async () => {
                            await window.initPixi({
                                bridge: tag,
                                token: tokenUser, // nullable - token game
                                assetUrl,
                                homeUrl: '/?type=modal-login',
                                supportUrl: @json($liveChatLink), //https://secure.livechatinc.com/licence/XXXXXXXX/v2/open_chat.cgi
                                paymentUrl: '/account/deposit/codepay',
                            });
                            const game = await new Promise((r) => {
                                const t = setInterval(() => {
                                    if (window[script]) {
                                        clearInterval(t);
                                        r(window[script]);
                                    }
                                }, 100);
                            });
                            game.start();
                        };
                        document.head.appendChild(script);
                    });
            }

            document.addEventListener('DOMContentLoaded', function() {
                userActivityWrapper(()=>{
                    window.handleRenderQuickbet(@json($token));
                });
            })
            
        </script>
        <script>
            $('.js-livestreamlist-tab-item').on('click', function(event) {
                event.preventDefault();
                $('.js-livestreamlist-tab-item').removeClass('active');
                $(this).addClass('active');
                const tab = $(this).data('tab');
                $(`.livestreamlist__content-hot, .livestreamlist__content-new`).addClass('opacity-0 visible h-0');
                $(`#${tab}`).removeClass('opacity-0 visible h-0');
            });

            window.addEventListener('DOMContentLoaded', () => {
                const quickbetContainer = $('.quickbet-container');
                const quickbetCanvas = $('.quickbet-canvas');

                if (quickbetContainer) {
                    quickbetContainer.on('click', function () {
                        const cookies = getCookies();
                        const user = cookies?.user ? JSON.parse(cookies.user) : null;

                        if (!user) {
                            openLogin();
                            sessionStorage.setItem('authForQuickbet', true);

                        } else {
                            if (user?.is_updated_fullname == 0) {
                                openChangeName();
                                sessionStorage.setItem('authForQuickbet', true);
                            }
                        }
                    })
                }
            })
        </script>
        @vite('resources/js/home/<USER>')
    @endPushOnce
@endif
