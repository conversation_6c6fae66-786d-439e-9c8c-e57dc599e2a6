@php
    $isMobile =  App\Helpers\DetectDeviceHelper::isMobile();
@endphp

<a href="/song-bai-livecasino-truc-tuyen?filter=&type=&keyword=&p=vingame" aria-label="hero-banner" class="block h-full w-full cursor-pointer">
    <div class="relative h-full xl:container ">
        <img 
            @if (!$isMobile) loading="lazy" fetchpriority="low" @endif
            alt="techplay"
            src="{{ asset('/asset/images/home/<USER>/techplay/techplay-mb.avif') }}"
            class="block w-full h-full xl:hidden"
        />
        <div class="relative z-[1] hidden justify-start items-start h-full pt-[76px] xl:flex">
            <img 
                @if ($isMobile) loading="lazy" fetchpriority="low" @endif
                src="{{ asset('/asset/images/home/<USER>/techplay/techplay-content.avif') }}"
                class="relative z-[1] w-[40%] aspect-auto 2xl:w-[49.91%]"
                alt="techplay"
            />
            <img 
                @if ($isMobile) loading="lazy" fetchpriority="low" @endif
                alt="techplay"
                src="{{ asset('/asset/images/home/<USER>/techplay/techplay-background.avif') }}"
                class="absolute bottom-0 right-0 z-0 w-[73%] aspect-[929/553] object-cover 2xl:left-[37.5%] 2xl:right-auto 2xl:w-[74.91%]"
            />
        </div>
    </div>
</a>
