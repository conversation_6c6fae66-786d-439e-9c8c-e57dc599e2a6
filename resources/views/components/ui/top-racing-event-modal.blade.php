<div id="top-racing-event-modal" class="fixed inset-0 z-[1001] flex justify-center items-center px-[10px] pt-[7px] bg-black bg-opacity-70 border-box overflow-auto xl:pt-[1px]">
    <div id="top-racing-event-modal-container" class="relative flex flex-col items-center gap-[2px] w-full max-w-[370px] xl:max-w-[834px]">
        <picture>
            <source media="(max-width: 1199px)" srcset="{{ asset('/asset/images/event/top-racing/bg-mb.avif') }}">
            <img src={{ asset('/asset/images/event/top-racing/bg-pc.avif') }} class="w-full aspect-[370/500] xl:aspect-[834/409]" alt="event"/>
        </picture>
        <button class="top-racing-event-modal-deposit absolute bottom-[8.2%] left-1/2 translate-x-[-50%] w-[184px] h-[48px] xl:bottom-[17.97%] xl:left-[56%] xl:translate-x-0 xl:w-[153px] xl:h-[40px] xl:hover:opacity-80 brightness-110 transition-all duration-200">
            <img 
                class="w-full h-full"
                alt="racing"
                src="{{ asset('/asset/images/event/top-racing/button.avif') }}"
            />
        </button>
        <button class="top-racing-event-modal-close absolute top-0 right-0 size-8 xl:hover:opacity-80 brightness-110 transition-all duration-200">
            <img 
                alt="close"
                class="aspec-[370/384]"
                src="{{ asset('/asset/images/event/close.avif') }}"
            />
        </button>
        <label class="absolute top-[101.2%] left-1/2 translate-x-[-50%] flex justify-center items-center gap-2 w-max cursor-pointer xl:bottom-[8.8%] xl:top-auto xl:left-[56.59%] xl:translate-x-0">
            <input type="checkbox" hidden class="top-racing-event-modal-show peer">
            <img class="block size-5 peer-checked:hidden" src="{{ asset('/asset/images/event/checkbox.avif') }}" alt="checkbox"/>
            <img class="hidden size-5 peer-checked:block" src="{{ asset('/asset/images/event/checkbox-active.avif') }}" alt="checkbox"/>
            <p class="text-[14px] leading-[20px] text-neutral">Không hiển thị lại</p>
        </label>
    </div>
</div>
