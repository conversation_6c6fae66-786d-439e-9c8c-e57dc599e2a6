@props(['hotMatch'])
@use(App\Enums\GatewayEndpoint)
@use(App\Enums\UrlPathEnum)
<div
    onclick="openSport({
        link: '{{ UrlPathEnum::K_SPORTS }}' + '?leagueId={{ $hotMatch->league_id }}&matchId={{ $hotMatch->match_id }}',
        apiUrl: '{{ GatewayEndpoint::K_SPORTS }}',
        loginRequired: '{{ $hotMatch->loginRequired ?? false }}',
        params: {
            leagueId: '{{ $hotMatch->league_id }}',
            matchId: '{{ $hotMatch->match_id }}',
        }
    })"
    class="flex flex-col h-full rounded-[8px] cursor-pointer w-full relative bg-neutral-50 border border-neutral-100 shadow-hotmatch-card py-[7px] px-[11px] xl:shadow-none xl:border-none xl:bg-neutral xl:p-[12px] xl:pb-[10px] xl:max-w-[301px] xl:rounded-xl">
    <div class="grid grid-cols-[1fr_auto] items-center gap-2 mb-2 xl:mb-[10px]">
        <span class="text-[10px] leading-[14px] text-neutral-1000 truncate">{{ $hotMatch->league_name_text }}</span>
        <div class="flex justify-between items-center">
            <div class="flex gap-1 text-[10px] leading-[14px]">
                <span class="text-neutral-1000 match-time-{{ $hotMatch->match_id }}"></span>
                <span class="text-neutral-1000 match-date-{{ $hotMatch->match_id }}"></span>
            </div>
        </div>
    </div>
    <div class="flex flex-col justify-between grow">
        <div class="grid grid-cols-[1fr_32px_1fr] items-center gap-[8px] mb-2 xl:mb-[18px] xl:py-[2px]">
            <div class="grid grid-cols-[1fr_26px] items-center gap-1.5 xl:grid-cols-[1fr_32px] xl:gap-2">
                <span class="max-w-full text-[12px] leading-[18px] text-right text-neutral-1200 truncate">{{ $hotMatch->teams[0]->name ?? '-' }}</span>
                <img src="{{ $hotMatch->teams[0]->flag_thumbnail }}" alt="hotmatch"
                    class="size-[26px] xl:size-[32px] object-contain" loading="lazy" fetchpriority="low"
                    onerror="this.onerror=null; this.src='{{ asset('asset/images/default-sport.avif') }}'"
                />
            </div>
            <div class="text-[16px] leading-[19px] font-bold text-center text-neutral-1200 xl:text-[18px] xl:leading-[26px] xl:text-neutral-1000">VS</div>
            <div class="grid grid-cols-[26px_1fr] items-center gap-1.5 xl:grid-cols-[32px_1fr] xl:gap-2">
                <img src="{{ $hotMatch->teams[1]->flag_thumbnail }}" alt="hotmatch"
                    class="size-[26px] xl:size-[32px] object-contain" loading="lazy" fetchpriority="low"
                    onerror="this.onerror=null; this.src='{{ asset('asset/images/default-sport.avif') }}'"
                />
                <span class="max-w-full text-[12px] leading-[18px] text-neutral-1200 truncate">{{ $hotMatch->teams[1]->name ?? '-' }}</span>
            </div>
        </div>
        <div class="grid grid-cols-2 gap-x-[10px] gap-y-2 xl:gap-x-[9px] xl:gap-y-1 min-w-[120px] xs:min-w-[150px] xl:min-w-[176px]">
            <div
                class="py-[3px] px-2 xl:p-[5.5px] xl:px-[8px] bg-black-5 xl:bg-neutral-50 rounded text-xs flex xl:gap-[7px] gap-[9px] justify-center items-center w-full leading-[18px] font-medium">
                <div class="w-1/3 text-neutral-800 font-bold">H</div>
                <span class="w-1/3 text-center text-neutral-1000">{{ isset($hotMatch->hdp->hTeam->rate) ? $hotMatch->hdp->hTeam->rate : '-' }}</span>
                <span
                    class="@if (isset($hotMatch->hdp->hTeam->odds) && floatval($hotMatch->hdp->hTeam->odds) < 0) text-danger-600 @else text-success-600 @endif w-1/3 text-right">{{ $hotMatch->hdp->hTeam->odds ?? '-' }}</span>
            </div>
            <div
                class="py-[3px] px-2 xl:p-[5.5px] xl:px-[8px] bg-black-5 xl:bg-neutral-50 rounded text-xs flex xl:gap-[7px] gap-[9px] justify-center items-center w-full leading-[18px] font-medium">
                <div class="w-1/3 text-neutral-800 font-bold">O</div>
                <span class="w-1/3 text-center text-neutral-1000">{{ $hotMatch->ou->hTeam->rate ?? '-' }}</span>
                <span
                    class="@if (isset($hotMatch->ou->hTeam->odds) && floatval($hotMatch->ou->hTeam->odds) < 0) text-danger-600 @else text-success-600 @endif w-1/3 text-right">{{ $hotMatch->ou->hTeam->odds ?? '-' }}</span>
            </div>
            <div
                class="py-[3px] px-2 xl:p-[5.5px] xl:px-[8px] bg-black-5 xl:bg-neutral-50 rounded text-xs flex xl:gap-[7px] gap-[9px] justify-center items-center w-full leading-[18px] font-medium">
                <div class="w-1/3 text-neutral-800 font-bold">A</div>
                <span class="w-1/3 text-center text-neutral-1000">{{ $hotMatch->hdp->aTeam->rate ?? '-' }}</span>
                <span
                    class="@if (isset($hotMatch->hdp->aTeam->odds) && floatval($hotMatch->hdp->aTeam->odds) < 0) text-danger-600 @else text-success-600 @endif w-1/3 text-right">{{ $hotMatch->hdp->aTeam->odds ?? '-' }}</span>
            </div>
            <div
                class="py-[3px] px-2 xl:p-[5.5px] xl:px-[8px] bg-black-5 xl:bg-neutral-50 rounded text-xs flex xl:gap-[7px] gap-[9px] justify-center items-center w-full leading-[18px] font-medium">
                <div class="w-1/3 text-neutral-800 font-bold">U</div>
                <span class="w-1/3 text-center text-neutral-1000">{{ $hotMatch->ou->aTeam->rate ?? '-' }}</span>
                <span
                    class="@if (isset($hotMatch->ou->hTeam->odds) && floatval($hotMatch->ou->aTeam->odds) < 0) text-danger-600 @else text-success-600 @endif w-1/3 text-right">{{ $hotMatch->ou->aTeam->odds ?? '-' }}</span>
            </div>
        </div>

    </div>
</div>

<script>
    (function() {
        const dateObj = new Date('{{ $hotMatch->text_time }}');

        const time = dateObj.toLocaleTimeString('en-US', {
            hour12: false,
            hour: '2-digit',
            minute: '2-digit'
        });
        const date_str = dateObj.toLocaleDateString('en-GB', {
            day: '2-digit',
            month: '2-digit'
        });
        const times = document.querySelectorAll('.match-time-{{ $hotMatch->match_id }}');
        const dates = document.querySelectorAll('.match-date-{{ $hotMatch->match_id }}');

        times.forEach(item => item.innerHTML = time);
        dates.forEach(item => item.innerHTML = date_str);
    })();
</script>
