@props([
    'data' => null,
])

@php
    $statusClass = '';
    
    if (isset($data['status'])) {
        if ($data['status'] === 'before') {
            $statusClass = 'w-[79px] xl:w-[95px]';
        } elseif ($data['status'] === 'expire') {
            $statusClass = 'w-[72px] xl:w-[87px]';
        } else {
            $statusClass = 'w-[76px] xl:w-[90px]';
        }
    }
@endphp
@if (isset($data['date_end']) && $data['date_end']< now())
    @php
        $data['link'] = '';
    @endphp
@endif
@if (isset($data['checkAuth']) && $data['checkAuth'] && !Auth::check())
    <a onclick="openLogin()" class="flex flex-col w-full h-max rounded-[8px] shadow-shadow-4px overflow-hidden relative">
        <picture class="pointer-events-none">
            <source media="(min-width: 1200px)" srcset="{{ asset($data['image']) }}"
                class="w-full aspect-[292/121] xl:aspect-[610/202]">
            <img src={{ asset($data['image-mb']) }} class="w-full aspect-[292/121] xl:aspect-[610/202]"
                alt="promotion-data-image" />
        </picture>
        <div class="flex flex-col gap-[2px] px-[10px] py-2 bg-neutral xl:px-4 xl:pt-[14px] xl:pb-4">
            <p
                class="text-[12px] leading-[18px] text-neutral-1000 capitalize xl:text-[16px] xl:leading-[24px] font-medium truncate">
                {{ $data['title'] }}</p>
            <p class="text-[10px] leading-[14px] text-neutral-800 xl:text-[14px] xl:leading-[20px]">
                {{ $data['description'] }}</p>
        </div>
        @if (isset($data['cardType']) && $data['cardType'] === 'event')
            @if (isset($data['date_end']) && $data['date_end']< now())
                <picture class="pointer-events-none">
                    <source media="(min-width: 1200px)" srcset="{{ asset('/asset/images/events/expire.avif') }}">
                    <img 
                        class="absolute top-[6px] left-[6px] h-[18px] xl:top-[9px] xl:left-[9px] xl:h-5 {{ $statusClass }}"
                        src="{{ asset('/asset/images/events/expire-mb.avif') }}"
                    />
                </picture>
            @else
                <picture class="pointer-events-none">
                    <source media="(min-width: 1200px)" srcset="{{ asset('/asset/images/events/valid.avif') }}">
                    <img 
                        class="absolute top-[6px] left-[6px] h-[18px] xl:top-[9px] xl:left-[9px] xl:h-5 {{ $statusClass }}"
                        src="{{ asset('/asset/images/events/valid-mb.avif') }}"
                    />
                </picture>
            @endif
        @endif
    </a>
@elseif (isset($data['checkAuth']) && $data['checkAuth'] && Auth::check() && isset($data['type']) && $data['type']=== 'game')
    <a href="{{ $data['link'] .'?token='. (Auth::user()->tp_token ?? '') }}"
        class="flex flex-col w-full h-max rounded-[8px] shadow-shadow-4px overflow-hidden relative"
        target="{{ App\Helpers\DetectDeviceHelper::isMobile() ? '_blank' : '_self' }}">
        <picture class="pointer-events-none">
            <source media="(min-width: 1200px)" srcset="{{ asset($data['image']) }}"
                class="w-full aspect-[292/121] xl:aspect-[610/202]">
            <img src={{ asset($data['image-mb']) }} class="w-full aspect-[292/121] xl:aspect-[610/202]"
                alt="promotion-data-image" />
        </picture>
        <div class="flex flex-col gap-[2px] px-[10px] py-2 bg-neutral xl:px-4 xl:pt-[14px] xl:pb-4">
            <p
                class="text-[12px] leading-[18px] text-neutral-1000 capitalize xl:text-[16px] xl:leading-[24px] font-medium truncate">
                {{ $data['title'] }}</p>
            <p class="text-[10px] leading-[14px] text-neutral-800 xl:text-[14px] xl:leading-[20px]">{{ $data['description']
                }}</p>
        </div>
        @if (isset($data['cardType']) && $data['cardType'] === 'event')
            @if (isset($data['date_end']) && $data['date_end']< now())
                <picture class="pointer-events-none">
                    <source media="(min-width: 1200px)" srcset="{{ asset('/asset/images/events/expire.avif') }}">
                    <img 
                        class="absolute top-[6px] left-[6px] h-[18px] xl:top-[9px] xl:left-[9px] xl:h-5 {{ $statusClass }}"
                        src="{{ asset('/asset/images/events/expire-mb.avif') }}"
                    />
                </picture>
            @else
                <picture class="pointer-events-none">
                    <source media="(min-width: 1200px)" srcset="{{ asset('/asset/images/events/valid.avif') }}">
                    <img 
                        class="absolute top-[6px] left-[6px] h-[18px] xl:top-[9px] xl:left-[9px] xl:h-5 {{ $statusClass }}"
                        src="{{ asset('/asset/images/events/valid-mb.avif') }}"
                    />
                </picture>
            @endif
        @endif
    </a>
@else
    @if ($data['link'])
        <a href="{{ $data['link'] }}" class="relative flex flex-col w-full h-max rounded-[8px] shadow-shadow-4px overflow-hidden">
    @else
        <span class="relative flex flex-col w-full h-max rounded-[8px] shadow-shadow-4px overflow-hidden cursor-default">
    @endif
        <picture class="pointer-events-none">
            <source media="(min-width: 1200px)" srcset="{{ asset($data['image']) }}"
                class="w-full aspect-[292/121] xl:aspect-[610/202]">
            <img src={{ asset($data['image-mb']) }} class="w-full aspect-[292/121] xl:aspect-[610/202]"
                alt="promotion-data-image" />
        </picture>
        <div class="flex flex-col gap-[2px] px-[10px] py-2 bg-neutral xl:px-4 xl:pt-[14px] xl:pb-4">
            <p
                class="text-[12px] leading-[18px] text-neutral-1000 capitalize xl:text-[16px] xl:leading-[24px] font-medium truncate">
                {{ $data['title'] }}</p>
            <p class="text-[10px] leading-[14px] text-neutral-800 xl:text-[14px] xl:leading-[20px]">{{ $data['description']
                }}</p>
        </div>
        @if (isset($data['cardType']) && $data['cardType'] === 'event')
            @if (isset($data['status']))
                <picture class="pointer-events-none">
                    <source media="(min-width: 1200px)" srcset="{{ asset('/asset/images/events/' . $data['status'] . '.avif') }}">
                    <img 
                        class="absolute top-[6px] left-[6px] h-[18px] xl:top-[9px] xl:left-[9px] xl:h-5 {{ $statusClass }}"
                        src="{{ asset('/asset/images/events/' . $data['status'] . '-mb.avif') }}"
                    />
                </picture>
            @else
                @if (isset($data['date_end']) && $data['date_end']< now())
                    <picture class="pointer-events-none">
                        <source media="(min-width: 1200px)" srcset="{{ asset('/asset/images/events/expire.avif') }}">
                        <img 
                            class="absolute top-[6px] left-[6px] h-[18px] xl:top-[9px] xl:left-[9px] xl:h-5 {{ $statusClass }}"
                            src="{{ asset('/asset/images/events/expire-mb.avif') }}"
                        />
                    </picture>
                @else
                    <picture class="pointer-events-none">
                        <source media="(min-width: 1200px)" srcset="{{ asset('/asset/images/events/valid.avif') }}">
                        <img 
                            class="absolute top-[6px] left-[6px] h-[18px] xl:top-[9px] xl:left-[9px] xl:h-5 {{ $statusClass }}"
                            src="{{ asset('/asset/images/events/valid-mb.avif') }}"
                        />
                    </picture>
                @endif
            @endif
        @endif
        
    @if ($data['link'])
        </a>
    @else
        </span>
    @endif
@endif