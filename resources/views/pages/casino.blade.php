@php
    $brandName = strtolower(config('app.brand_name'));
    $heroBanner = config('home.heroBanner');
    if (!Auth::user() && request()->is('song-bai-livecasino-truc-tuyen/favorite')) {
        header('Location: /song-bai-livecasino-truc-tuyen');
        exit();
    }

    $showLiveHighlight = request()->route('slug') !== 'favorite';
@endphp
<x-layout>   
    <div class="banner">
        <section class="xl:block hidden" style="background-image: url('{{ asset('/asset/images/games/bg-banner-casino.avif') }}'); background-size: 100% 100%">
            <div class="container justify-end relative flex h-[145px]">
                <div
                    class="flex flex-col gap-2 absolute left-[10px] top-[32px]">
                    <div class="text-[48px] leading-[60px] uppercase text-neutral-1000 font-bold">Live Casino</div>
                    <div class="text-[20px] font-light leading-6 text-neutral-1000">
                        <PERSON><PERSON><PERSON><PERSON> phá thế giới giải trí đỉnh cao cùng <span class="uppercase">{{ $brandName }}</span>
                    </div>
                </div>
                <img 
                    src="{{ asset('asset/images/games/banner-casino.avif') }}" 
                    class="w-[590px] object-cover" 
                    alt="icon"
                    alt="banner" />
            </div>
        </section>
        <section class="xl:hidden block">
            <x-ui.home.hero-banner 
                class="!pb-0" 
                :swiperConfig="$heroBanner['swiperConfig']" 
                :list="$heroBanner['list']">
            </x-ui.home.hero-banner>
        </section>
    </div>
    <div class="container games {{$showLiveHighlight ? '!pr-0 xl:pr-[10px]' : ''}}">
        <x-ui.breadcrumb :list="[['name' => $breadCrumbText, 'url' => '/song-bai-livecasino-truc-tuyen']]"></x-ui.breadcrumb>
        <x-ui.navigation-mb class="!top-[86px]"></x-ui.navigation-mb>
        <div class="overflow-hidden xl:overflow-visible pl-[10px] xl:pr-0 xl:pl-0">
            <x-ui.card-container 
                :$games 
                :$activeFilter 
                :$filters 
                :$routeUrl 
                :$swiperConfig 
                :$gamesTotal 
                type="casino" 
                :streamGames="$streamGames"
                :newStreamGames="$newStreamGames"
            />
        </div>
    </div>

    @pushOnce('scripts')
        <script src="{{ asset('js/nanoplayer.4.min.js') }}" defer></script>
        @vite('resources/js/home/<USER>')
    @endPushOnce

</x-layout>
