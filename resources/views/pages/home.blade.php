@php
    $heroBanner = config('home.heroBanner');
    $gameSection = config('home.gameSection');
    $navigationSection = config('home.navigationSection');
    $topGamesSection = config('home.topGamesSection');
    $sportSwiperConfig = config('home.sportSwiperConfig');

    use App\Helpers\DetectDeviceHelper;
    $isMobile = DetectDeviceHelper::isMobile();
@endphp

<x-layout>
    <div class="relative z-0">
        <x-ui.home.hero-banner :swiperConfig="$heroBanner['swiperConfig']" :list="$heroBanner['list']"></x-ui.home.hero-banner>
        @if (!empty($nearWin) && !$isMobile)
            <div class="bg-neutral-50 xl:block hidden">
                <x-ui.home.topjackpot-section :nearWin="$nearWin" />
            </div>
        @endif
        <div class="pl-[10px] xl:p-0">
            
            <x-ui.navigation-mb></x-ui.navigation-mb>

            <div class="overflow-hidden">
                <x-ui.home.quickbet :gameList="$data['streamGames']" :newGameList="$newStreamGames" />

                @if (!$isMobile)
                    <div class="hidden xl:block mb-4 xl:mb-[40px]">
                        <x-ui.home.sports :sportSwiperConfig="$sportSwiperConfig" swiperRequiredClass="home-sports-swiper" :hotMatches="$hotMatches" />
                    </div>
                @endif

                <x-ui.home.lottery-section class="mb-4 xl:mb-[40px]" :title="$topGamesSection['title']" :titleHighlight="$topGamesSection['titleHighlight']"></x-ui.home.lottery-section>
                
                @if (is_array($data['nohuGames']) && count($data['nohuGames']) > 0)
                    <div id="js-pc-loading-home-section">
                        <div class="js-pc-home-content-loaded">
                            <x-ui.home.topgame-section class="mb-4 xl:mb-[40px]" :list="$data['nohuGames']" :swiperConfig="$topGamesSection['swiperConfig']"
                                :swiperRequiredClass="$topGamesSection['swiperRequiredClass']" :title="$topGamesSection['title']" :titleHighlight="$topGamesSection['titleHighlight']">
                            </x-ui.home.topgame-section>
                        </div>
                    </div>
                @endif

                <x-ui.home.promotions />
                
                @if (!$isMobile)
                    <div class="max-xl:hidden bg-news-gradient py-[20px]">
                        <div class="container grid grid-cols-2 gap-6">
                            <x-ui.home.news :listNews="$listNews" />
                            <x-ui.home.instruct />
                        </div>
                    </div>
                @endif

            </div>
        </div>
    </div>
</x-layout>
