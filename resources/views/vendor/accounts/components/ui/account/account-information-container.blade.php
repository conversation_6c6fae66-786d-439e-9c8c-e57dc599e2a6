@props([''])
<div class="w-full p-4 bg-neutral xl:rounded-xl">
    <div class="flex items-center gap-x-2 mb-4">
        <img src="{{asset('/vendor/accounts/images/account/information/profile.svg')}}" alt="profile" class="w-10 h-10" />
        <h2 class="text-base xl:text-lg font-semibold">{{__('account.account_information.title')}}</h2>
    </div>
    <div class="flex">
        <div class="w-full">
            @if (Auth::check())
            <div id="change-information" class="w-full">
                <div class="mb-4">
                    <label class="mb-2 block text-sm font-medium text-grey-600">
                        {{__('account.account_information.username_lb')}}
                    </label>

                    <input
                        type="text"
                        id="username"
                        name="username"
                        value="{{Auth::user()->username ?? ''}}"
                        disabled
                        placeholder="joedoe01"
                        class="block w-full appearance-none rounded-md bg-white p-2.5 focus:ring-0 !text-base bg-bigg-gray border-[#ECECEC] focus:border-secondary-100" />
                </div>
                @if (Auth::user()->username !== Auth::user()->fullname)
                <div class="mb-4">
                    <label class="mb-2 block text-sm font-medium text-grey-600">
                        {{__('account.account_information.fullname_lb')}}
                    </label>

                    <input
                        type="text"
                        id="fullname"
                        name="fullname"
                        value="{{Auth::user()->fullname ?? ''}}"
                        disabled
                        class="block w-full appearance-none rounded-md bg-white p-2.5 focus:ring-0 !text-base bg-bigg-gray border-[#ECECEC] focus:border-secondary-100" />
                </div>
                @endif

                <div class="mb-4">
                    <label class="mb-2 block text-sm font-medium text-grey-600">
                        {{__('account.account_information.phone_lb')}}
                    </label>

                    <input
                        id="phone"
                        type="text"
                        disabled
                        value="{{Auth::user()->phone ?? ''}}"
                        class="border block w-full appearance-none rounded-md bg-white p-2.5 focus:ring-0 !text-base bg-bigg-gray border-[#ECECEC] focus:border-secondary-100" />
                </div>

                <div class="mb-4">
                    <label class="mb-2 block text-sm font-medium text-grey-600">
                        {{__('account.account_information.email_lb')}}
                    </label>
                    <div id="input-container">
                        <div class="relative">
                            @if (empty(Auth::user()->email))

                            <input
                                id="email"
                                name="email"
                                type="email"
                                placeholder="{{__('account.account_information.email_pld')}}"
                                class="border-solid w-full p-2.5 border border-gray-300 rounded-md focus:outline-none text-black" />
                            <input
                                id="otp"
                                name="otp"
                                type="text"
                                placeholder="{{__('account.account_information.otp_pld')}}"
                                class="border-solid w-full p-2.5 border border-gray-300 rounded-md focus:outline-none text-black hidden" />
                            @else
                            <input
                                type="email"
                                value="{{Auth::user()->email ?? ''}}"
                                disabled
                                placeholder="{{__('account.account_information.email_pld')}}"
                                class="border-solid w-full p-2.5 border border-gray-300 rounded-md focus:outline-none text-black" />
                            @endif
                            <span class="mt-4 text-danger-500 text-sm" id="email-info-error"></span>
                            <div id="resend-container" class="mt-2 text-sm text-neutral-600 hidden">
                                <span id="back-email" class="text-secondary-400 cursor-pointer">{{__('account.account_information.back_email')}}</span>
                                <span id="resend-text" class="text-secondary-400 cursor-pointer">{{__('account.account_information.resend_OTP')}}</span>
                                <span id="countdown" class="text-danger-500"></span>
                            </div>
                        </div>
                    </div>
                </div>

                @if (!empty(Auth::user()->tele_chat_id))
                <div class="mb-4">
                    <label class="mb-2 block text-sm font-medium text-grey-600">
                        {{__('account.account_information.telegram_lb')}}
                    </label>
                    <div>
                        <div class="relative">
                            <input
                                id="telegram"
                                name="telegram"
                                type="text"
                                value="{{Auth::user()->tele_chat_id}}"
                                disabled
                                class="block w-full p-2.5 border border-gray-300 rounded-md focus:outline-none focus:ring focus:ring-secondary-100 text-black" />
                        </div>
                    </div>
                </div>
                @endif

                <button
                    type="button"
                    id="submit-btn-information"
                    class="w-full p-2 h-11 bg-secondary-400 hover:brightness-110 rounded-lg text-neutral font-semibold">
                    {{ __('account.update') }}
                </button>
                <button
                    type="button"
                    id="submit-btn-verify"
                    class="w-full p-2 h-11 bg-secondary-400 hover:brightness-110 rounded-lg text-neutral font-semibold hidden">
                    {{ __('account.verify_OTP') }}
                </button>

                @if (empty(Auth::user()->tele_chat_id))
                <div class="verify-telegram mt-6 p-3 rounded-md border border-solid border-neutral-300">
                    <div class="font-semibold text-sm text-neutral-800 mb-1">{{__('account.change_password.current_password_pld')}}</div>
                    <div class="flex w-full justify-between items-center">
                        <div class="flex gap-x-2 items-center">
                            <img src="{{asset('/vendor/accounts/images/account/information/telegram.svg')}}" alt="telegram" class="w-10 h-10 object-contain" />
                            <span class="text-[#000] font-medium text-base">Telegram</span>
                        </div>
                        <div class="text-secondary-400 font-semibold text-base cursor-pointer capitalize" id="btn-verify-telegram">
                            {{__('account.account_information.verify_telegram_btn')}}
                        </div>
                    </div>
                </div>
                @endif
            </div>
            @endif
        </div>
    </div>
</div>
@push('scripts')
<script>
    window.addEventListener("load", async (event) => {
        const $emailInput = $('#email');
        const $otp = $('#otp');
        const $emailError = $('#email-info-error');
        const $otpError = $('<span class="mt-4 text-danger-500 text-sm" id="otp-info-error"></span>');
        $otp.after($otpError);
        const $submitButton = $('#submit-btn-information');
        const $submitVerify = $('#submit-btn-verify');
        const $backEmail = $('#back-email');
        const $resendText = $('#resend-text');
        const $countdown = $('#countdown');
        const $resendContainer = $('#resend-container');

        let isLoading = false;
        let countdownInterval;

        $resendContainer.hide();
        $submitVerify.hide();

        const isDisabled = $emailInput.is(':disabled');
        if (isDisabled) {
            $submitButton.prop('disabled', true);
        }

        const validateField = (field, errorElement, customMessage = '') => {
            const value = field.val().trim();
            let errorMessage = customMessage;

            if (field.attr('id') === 'email') {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!value) {
                    errorMessage = '{{__("account.account_information.required_email")}}';
                } else if (!emailRegex.test(value)) {
                    errorMessage = '{{__("account.account_information.invalid_email")}}';
                }
            }

            if (!value && field.attr('id') === 'otp') {
                errorMessage = '{{__("account.account_information.required_otp")}}';
            }

            if (!!errorMessage) {
                field.addClass('error');
                errorElement.text(errorMessage);
                return false;
            } else {
                field.removeClass('error');
                errorElement.text('');
                return true;
            }
        };

        const startCountdown = (duration) => {
            let timeLeft = duration;
            $resendText.hide();
            $countdown.text(` (${timeLeft}s)`);
            countdownInterval = setInterval(() => {
                timeLeft -= 1;
                if (timeLeft > 0) {
                    $countdown.text(` (${timeLeft}s)`);
                } else {
                    clearInterval(countdownInterval);
                    $countdown.text('');
                    $resendText.show();
                }
            }, 1000);
        };

        $resendText.on('click', function() {
            startCountdown(60);
            $otp.val('');
        });

        $backEmail.on('click', function() {
            clearInterval(countdownInterval);
            $otp.val('');
            $otp.hide();
            $resendContainer.hide();
            $emailInput.show();
            $submitButton.show();
            $submitVerify.hide();
        });

        $emailInput.on('input', function() {
            validateField($(this), $emailError);
        });

        $otp.on('input', function() {
            validateField($(this), $otpError);
        });

        $submitButton.on('click', debounce(async function(e) {
            clearInterval(countdownInterval);
            if (!validateField($emailInput, $emailError)) {
                e.preventDefault();
            } else {
                const params = {
                    email: $emailInput.val().trim()
                }
                const res = await fetchData('/user/getOTP', params, {}, '');
                if (res.code === 200 && res.status === 'OK') {
                    $emailInput.hide();
                    $otp.show();
                    $resendContainer.show();
                    startCountdown(60);
                    $submitButton.hide();
                    $submitVerify.show();
                }
            }

        }));

        $submitVerify.on('click', debounce(async function(e) {
            if (!validateField($otp, $otpError)) {
                e.preventDefault();
            } else {
                const params = {
                    code: $otp.val().trim()
                };
                const res = await submitData('/user/emailOTPVerification', params, '');
                isLoading = false;
                if (res?.status === 'ERROR') {
                    useToast('error', res?.message || '');
                } else if (res?.status === 'OK' && res?.code === 200) {
                    useToast('success', res?.message || '');
                    setTimeout(() => {
                        window.location.reload();
                    }, 500);
                }
            }
        }));
    });
</script>
@endpush

@pushOnce('scripts')
    @vite('resources/js/input.js')
@endPushOnce