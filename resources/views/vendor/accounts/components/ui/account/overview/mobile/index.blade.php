<div>
    <div id="mobile-overview-menu">
        @if (request()->get('tab') === 'overview')
            <div id="mobile-overview-container">
                <div class="mb-3">
                    <div class="flex items-center gap-2 mb-[0.625rem]">
                        <img src="{{ asset('/vendor/accounts/images/account/avatar-overview-mb.avif') }}" alt="avatar"
                            class="w-[46px] h-[46px] aspect-square object-contain">
                        <div class="w-full">
                            <div class="flex flex-col items-start justify-center w-full">
                                <p class="font-medium text-neutral-1000 w-full">
                                    {{ Auth::user()->fullname ?? (Auth::user()->username ?? '') }}
                                </p>
                                <div class="flex items-center gap-1">
                                    <p class="text-xs font-normal text-neutral-800 leading-[calc(18/12)]">
                                        {{ __('account-overview.wallet.balance') }}:</p>
                                    <p class="text-sm font-medium text-neutral-1000 js-account-balance">
                                        {{ number_format(Auth::user()->balance ?? 0, 0, '.', ',') }} K</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="flex flex-col bg-neutral-250 rounded-3xl">
                        <div class="flex flex-col gap-4 bg-neutral max-[385px]:px-2 max-[385px]:py-4 p-4 rounded-3xl">
                            <div class="grid grid-cols-4 gap-2 max-w-[380px] mx-auto">
                                <a href={{ route('en.deposit.index') }}>
                                    <div
                                        class="h-[4.625rem] w-[4.71875rem] max-[389px]:min-w-[4.625rem] aspect-square rounded-2xl bg-overview-menu-item gap-1 flex flex-col items-center justify-center">
                                        <img src="{{ asset('/asset/icons/account/overview/deposit-mb.svg') }}"
                                            alt="deposit" class="w-7 h-auto aspect-square">
                                        <p class="text-xs text-neutral-1000 capitalize font-medium leading-[18px]">Nạp Tiền</p>
                                    </div>
                                </a>

                                <a href={{ route('en.withdraw.index') }}>
                                    <div
                                        class="h-[4.625rem] w-[4.71875rem] max-[389px]:min-w-[4.625rem] aspect-square rounded-2xl bg-overview-menu-item gap-1 flex flex-col items-center justify-center">
                                        <img src="{{ asset('/asset/icons/account/overview/withdraw-mb.svg') }}"
                                            alt="withdraw" class="w-7 h-auto aspect-square">
                                        <p class="text-xs text-neutral-1000 capitalize font-medium leading-[18px]">Rút tiền</p>
                                    </div>
                                </a>

                                <a href={{ route('en.information.index') }}>
                                    <div
                                        class="h-[4.625rem] w-[4.71875rem] max-[389px]:min-w-[4.625rem] aspect-square rounded-2xl bg-overview-menu-item gap-1 flex flex-col items-center justify-center">
                                        <img src="{{ asset('/asset/icons/account/overview/account-mb.svg') }}"
                                            alt="account" class="w-7 h-auto aspect-square">
                                        <p class="text-xs text-neutral-1000 capitalize font-medium leading-[18px]">Tài khoản</p>
                                    </div>
                                </a>

                                <a href={{ route('en.history.index') }}>
                                    <div
                                        class="h-[4.625rem] w-[4.71875rem] max-[389px]:min-w-[4.625rem] aspect-square rounded-2xl bg-overview-menu-item gap-1 flex flex-col items-center justify-center">
                                        <img src="{{ asset('/asset/icons/account/overview/history-mb.svg') }}"
                                            alt="history" class="w-7 h-auto aspect-square">
                                        <p class="text-xs text-neutral-1000 capitalize font-medium leading-[18px]">Lịch sử</p>
                                    </div>
                                </a>
                            </div>
                        </div>
                        @if (count($validTransaction) > 0)
                            <div class="pl-4 pt-4 pb-5 ">
                                <x-accounts::ui.account.overview.fast-deposit :validTransaction="$validTransaction" />
                            </div>
                        @endif
                        {{-- hide welcome steps --}}
                        <!-- @if ($userVerificationStatus->isVerified)
                            @if ($userVerificationStatus->isShowSection)
                                <div class="pt-3.5 pb-[18px] px-4">
                                    <div
                                        class="bonus-game flex items-center gap-[10px] h-[60px] p-[10px] rounded-[12px] border border-info-200 bg-overview-bonus">
                                        <img src="{{ asset('vendor/accounts/images/account/overview/bonus.avif') }}"
                                            class="w-[40px] h-[40px]" alt="bonus" />
                                        <div class="flex flex-col gap-1 items-start">
                                            <p class="text-[10px] leading-[14px] font-bold text-primary-500">
                                                THƯỞNG HOÀN THÀNH
                                            </p>
                                            <p class="text-[14px] leading-[20px] font-medium text-neutral-1000">
                                                10 vòng quay miễn phí
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            @endif
                        @else
                            <div class="pt-3.5 pb-[18px] px-4">
                                <button
                                    class="bonus-header flex justify-between items-center gap-[10px] w-full pl-[7.5px]">
                                    <div class="flex items-center gap-2 pointer-events-none">
                                        <img src="{{ asset('vendor/accounts/images/account/overview/user.avif') }}"
                                            class="w-[25px] h-[25px] mr-[7.5px]" alt="user" />
                                        <div class="flex flex-col items-start gap-[2px]">
                                            <p class="text-[16px] leading-[24px] font-medium text-neutral-1000">
                                                Thiếp lập tài khoản
                                            </p>
                                            <p class="text-[12px] leading-[18px] text-left text-neutral-800">
                                                Thiết lập ngay để nhận thưởng chào mừng.
                                            </p>
                                        </div>
                                    </div>
                                    <i
                                        class="icon-arrow-down text-[24px] text-neutral-600 pointer-events-none [.active-drop_&]:rotate-180"></i>
                                </button>
                                <div class="bonus-content hidden">
                                    <div class="flex flex-col gap-3 pt-3">
                                        <div class="flex flex-col gap-[10px]">
                                            <div class="flex items-center gap-[6px]">
                                                <span
                                                    class="size-[12px] rounded-full border-[3px] inline xl:hidden {{ $userVerificationStatus->deposit ? 'border-info-500' : 'border-neutral-300' }}"></span>
                                                <p class="text-[14px] leading-[20px] text-neutral-1000">
                                                    Thực hiện nạp lần đầu
                                                </p>
                                            </div>
                                            <div class="flex items-center gap-[6px]">
                                                <span
                                                    class="size-[12px] rounded-full border-[3px] inline xl:hidden {{ $userVerificationStatus->tele ? 'border-info-500' : 'border-neutral-300' }}"></span>
                                                <p class="text-[14px] leading-[20px] text-neutral-1000">
                                                    Xác minh Telegram
                                                </p>
                                            </div>
                                            <div class="flex items-center gap-[6px]">
                                                <span
                                                    class="size-[12px] rounded-full border-[3px] inline xl:hidden {{ $userVerificationStatus->bank ? 'border-info-500' : 'border-neutral-300' }}"></span>
                                                <p class="text-[14px] leading-[20px] text-neutral-1000">
                                                    Liên kết tài khoản ngân hàng
                                                </p>
                                            </div>
                                        </div>
                                        <div
                                            class="bonus-game flex items-center gap-[10px] h-[60px] p-[10px] rounded-[12px] bg-gradient-process-bar border border-info-200 bg-overview-bonus">
                                            <img src="{{ asset('vendor/accounts/images/account/overview/bonus.avif') }}"
                                                class="w-[40px] h-[40px]" alt="bonus" />
                                            <div class="flex flex-col gap-1 items-start">
                                                <p class="text-[10px] leading-[14px] font-bold text-primary-500">
                                                    THƯỞNG HOÀN THÀNH
                                                </p>
                                                <p class="text-[14px] leading-[20px] font-medium text-neutral-1000 capitalize">
                                                    10 vòng quay miễn phí
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif -->
                    </div>
                </div>
            </div>
            <x-accounts::ui.account.overview.mobile.tab 
                :hotGames="$hotGames" 
                :listTransaction="$listTransaction" 
                :promotionData="$promotionData" 
                :$slotInfo
                :$casinoInfo 
            />
    </div>
@else
    <x-accounts::ui.account.user-card />
    @endif
</div>
</div>

@pushOnce('scripts')
    @vite(['resources/js/account/overview.js'])
@endPushOnce
@if ($userVerificationStatus->is_show_freespin)
    <script>
        window.freeSpinsStart = '{{ $userVerificationStatus->created_date }}';
        window.freeSpinsExpired = '{{ $userVerificationStatus->expired_date }}';
    </script>
@endif
