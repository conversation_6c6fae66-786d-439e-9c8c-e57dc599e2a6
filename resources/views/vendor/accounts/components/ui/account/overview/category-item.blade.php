@props([
    'isMobile' => false,
    'categories' => []
])

<div class="flex flex-col justify-start items-center gap-3 w-full">
    @foreach ($categories as $category)
        <a 
            @if (!isset($category['isSport']) || (isset($category['isSport']) && !$category['isSport']))
                href="{{ $isMobile && isset($category['mb-link']) ? $category['mb-link'] :  $category['link'] }}"
            @else
                onclick="openSport({
                            link: '{{ $category['url'] ?? '' }}',
                            apiUrl: '{{ $category['apiUrl'] ?? '' }}'
                        })"
                class="cursor-pointer"
            @endif
        >
            {{-- pc mode --}}
            <div class="max-w-[9.25rem] aspect-[148/166] hover:opacity-70 hidden xl:block">
                <img src="{{ asset('/vendor/accounts/images/account/overview/category/' . $category['image'] . '-pc.avif') }}"
                    alt="{{ $category['name'] }}" class="w-full h-auto aspect-[148/166] object-cover">
            </div>
            {{-- mobile mode --}}
            <div class="max-w-[6.25rem] aspect-[100/113] xl:hover:opacity-70 xl:hidden">
                <img src="{{ asset('/vendor/accounts/images/account/overview/category/' . $category['image'] . '-mb.avif') }}"
                    alt="{{ $category['name'] }}" class="w-full h-auto aspect-[100/113] object-cover">
            </div>
        </a>
    @endforeach
</div>
