// Critical CSS - Above the fold content only
// This file contains only the essential styles needed for initial page render

// Font faces - Critical for preventing FOUT
@font-face {
    font-family: "SVN_VT_Redzone";
    src: url("../fonts/SVN_VT_Redzone_Classic.otf") format("opentype");
    font-weight: normal;
    font-style: normal;
    font-display: swap; // Improve font loading performance
}

@font-face {
    font-family: "UTM Facebook";
    src: url("../fonts/00091-UTM-Facebook.ttf") format("opentype");
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'SVN-Helvetica Neue Heavy';
    src: url('../fonts/SVN-HelveticaNeue-Heavy.ttf') format('opentype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

// Essential utilities that are used above the fold
@layer utilities {
    .no-scrollbar::-webkit-scrollbar {
        display: none;
    }
    .no-scrollbar {
        -ms-overflow-style: none;
        scrollbar-width: none;
    }
    
    .secondary-filter {
        filter: brightness(0) saturate(100%) invert(59%) sepia(100%) saturate(219%) hue-rotate(0deg) brightness(88%) contrast(90%);
    }
    
    .orange-gradient-filter {
        filter: brightness(0) saturate(100%) invert(56%) sepia(75%) saturate(5497%) hue-rotate(360deg) brightness(103%) contrast(104%);
    }
}

// Critical layout styles
.container {
    @apply px-2.5;
}

// Essential input styles
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

input[type="number"] {
    -moz-appearance: textfield;
}

input:focus-visible {
    @apply outline-none;
}

button:focus-visible {
    @apply outline-none;
}

// Critical header styles
header .menu-item {
    &:hover, &.active-link {
        p img {
            filter: brightness(0) saturate(100%) invert(30%) sepia(40%) saturate(500%) hue-rotate(10deg) brightness(140%) contrast(100%);
        }
    }
}

// Critical hero banner styles
.hero-banner {
    @media (min-width: 1200px) {
        background: url("../../public/asset/images/home/<USER>/bg-hero.avif") center center no-repeat;
        background-size: cover;
    }
}

// Essential modal z-index (critical for functionality)
#auth-modal {
    @apply absolute top-0 left-0 z-[54];
}

#noti-modal {
    @apply absolute top-0 left-0 z-[55];
}

// Critical game display styles
.game-type-active {
    filter: brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(0%) hue-rotate(338deg) brightness(102%) contrast(103%);
}

// Essential scrollbar styles for main content
.scrollbar {
    &::-webkit-scrollbar {
        @apply w-[4px];
    }
    &::-webkit-scrollbar-track {
        @apply bg-transparent;
    }
    &::-webkit-scrollbar-thumb {
        @apply bg-scrollbar;
    }
}

// Critical loading animation
.loader-image-transparent {
    background: transparent url("/resources/img/spinner.svg") center center no-repeat;
}

// Essential filter states
.filter-active {
    @apply xl:bg-primary-500 xl:text-neutral text-primary-500;
    i {
        @apply text-primary-500 xl:text-neutral;
    }
    .dropdown-label {
        @apply text-primary-600;
    }
}

.provider-active {
    @apply text-primary-500;
    i {
        @apply text-primary-500;
    }
    .dropdown-label {
        @apply text-primary-600;
    }
}

// Critical responsive utilities
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

// Critical accessibility
.grecaptcha-badge { 
    @apply invisible;
}

// Essential mobile styles
@supports (-webkit-touch-callout: none) {
    input:focus {
        touch-action: none;
    }
}
