// Optimized Image Loading Styles
.image-container {
    position: relative;
    overflow: hidden;
    
    img {
        opacity: 0;
        transition: opacity 0.3s ease-in-out;
        
        &.loaded {
            opacity: 1;
        }
        
        // Prevent layout shift
        &[width][height] {
            aspect-ratio: attr(width) / attr(height);
        }
    }
    
    picture {
        display: block;
        width: 100%;
        height: 100%;
        
        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
    }
}

// Skeleton loading animation
.skeleton {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        #f0f0f0 25%,
        #e0e0e0 50%,
        #f0f0f0 75%
    );
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: inherit;
    
    &.hidden {
        display: none;
    }
}

@keyframes skeleton-loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

// Responsive image utilities
.responsive-image {
    width: 100%;
    height: auto;
    
    &--cover {
        object-fit: cover;
    }
    
    &--contain {
        object-fit: contain;
    }
    
    &--fill {
        object-fit: fill;
    }
}

// Image aspect ratio utilities
.aspect-ratio {
    position: relative;
    
    &::before {
        content: '';
        display: block;
        padding-top: var(--aspect-ratio, 100%);
    }
    
    img, picture {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
    }
    
    // Common aspect ratios
    &--1-1 {
        --aspect-ratio: 100%;
    }
    
    &--4-3 {
        --aspect-ratio: 75%;
    }
    
    &--16-9 {
        --aspect-ratio: 56.25%;
    }
    
    &--3-2 {
        --aspect-ratio: 66.67%;
    }
    
    &--golden {
        --aspect-ratio: 61.8%;
    }
}

// Performance optimizations
img[loading="lazy"] {
    // Ensure lazy images don't cause layout shift
    min-height: 1px;
}

// Critical images (above the fold)
.critical-image {
    img {
        opacity: 1;
        
        &[loading="lazy"] {
            loading: eager;
        }
    }
    
    .skeleton {
        display: none;
    }
}

// Error state styling
.image-error {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f5f5;
    color: #999;
    font-size: 0.875rem;
    min-height: 100px;
    
    &::before {
        content: '🖼️';
        margin-right: 0.5rem;
    }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
    .skeleton {
        background: linear-gradient(
            90deg,
            #2a2a2a 25%,
            #1a1a1a 50%,
            #2a2a2a 75%
        );
    }
    
    .image-error {
        background-color: #2a2a2a;
        color: #666;
    }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
    .image-container img {
        transition: none;
    }
    
    .skeleton {
        animation: none;
        background: #f0f0f0;
    }
    
    @media (prefers-color-scheme: dark) {
        .skeleton {
            background: #2a2a2a;
        }
    }
}

// Print styles
@media print {
    .skeleton {
        display: none;
    }
    
    .image-container img {
        opacity: 1;
    }
}
