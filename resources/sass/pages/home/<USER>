.hero-banner {
    .hero-banner__wrapper {
        .swiper-pagination {
            @apply bottom-[12px] z-[1] max-w-[1260px] flex justify-start gap-[1px] px-[10px] max-xl:pl-[29px]  #{!important};

            span {
                @apply w-[12px] h-[2px] m-0 bg-neutral-100 opacity-100 rounded-[6px];

                &.swiper-pagination-bullet-active {
                    @apply bg-primary-500;
                }
            }
        }
    }

    @media (screen(xl)) {
        @apply p-0;

        .hero-banner__wrapper {
    
            .swiper-pagination {
                @apply left-1/2 bottom-[32%] !translate-x-[-50%] gap-1 #{!important} ;

                span {
                    @apply w-[44px] h-[6px] !m-0 rounded-[20px];
                }
            }
        }
    }
}
