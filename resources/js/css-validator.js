// CSS Optimization Validator
class CSSValidator {
    constructor() {
        this.criticalCSSSize = 0;
        this.nonCriticalCSSSize = 0;
        this.loadTimes = {};
        this.init();
    }

    init() {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.validateOptimization();
            });
        } else {
            this.validateOptimization();
        }
    }

    validateOptimization() {
        this.checkCriticalCSS();
        this.checkAsyncLoading();
        this.measurePerformance();
        this.generateReport();
    }

    checkCriticalCSS() {
        // Check if critical CSS is inlined
        const inlineStyles = document.querySelectorAll('style');
        let criticalCSSFound = false;
        let totalInlineSize = 0;

        inlineStyles.forEach(style => {
            const content = style.textContent || style.innerText;
            totalInlineSize += content.length;
            
            // Check for critical CSS indicators
            if (content.includes('@tailwind base') || 
                content.includes('.container') || 
                content.includes('font-face')) {
                criticalCSSFound = true;
            }
        });

        this.criticalCSSSize = totalInlineSize;
        
        console.log('🎨 Critical CSS Validation:');
        console.log(`   ✅ Inline styles found: ${inlineStyles.length}`);
        console.log(`   📏 Total inline CSS size: ${this.formatBytes(totalInlineSize)}`);
        console.log(`   ${criticalCSSFound ? '✅' : '❌'} Critical CSS detected`);
        
        if (totalInlineSize > 14000) {
            console.warn(`   ⚠️  Critical CSS size (${this.formatBytes(totalInlineSize)}) exceeds recommended 14KB`);
        }
    }

    checkAsyncLoading() {
        // Check for async CSS loading
        const preloadLinks = document.querySelectorAll('link[rel="preload"][as="style"]');
        const stylesheetLinks = document.querySelectorAll('link[rel="stylesheet"]');
        
        console.log('🔄 Async CSS Loading:');
        console.log(`   📄 Preload links: ${preloadLinks.length}`);
        console.log(`   📄 Stylesheet links: ${stylesheetLinks.length}`);
        
        // Check if CSS loader is available
        if (window.cssLoader) {
            console.log('   ✅ CSS loader available');
            console.log(`   📦 Loaded stylesheets: ${window.cssLoader.loadedStyles.size}`);
        } else {
            console.warn('   ❌ CSS loader not found');
        }
    }

    measurePerformance() {
        // Measure CSS loading performance
        if ('performance' in window) {
            const entries = performance.getEntriesByType('resource');
            const cssEntries = entries.filter(entry => 
                entry.name.includes('.css') || entry.initiatorType === 'css'
            );

            console.log('⚡ CSS Performance:');
            cssEntries.forEach(entry => {
                const loadTime = entry.responseEnd - entry.startTime;
                this.loadTimes[entry.name] = loadTime;
                console.log(`   📄 ${this.getFileName(entry.name)}: ${loadTime.toFixed(2)}ms`);
            });

            // Check for render-blocking resources
            const renderBlockingCSS = cssEntries.filter(entry => 
                entry.renderBlockingStatus === 'blocking'
            );
            
            if (renderBlockingCSS.length > 0) {
                console.warn(`   ⚠️  Render-blocking CSS files: ${renderBlockingCSS.length}`);
                renderBlockingCSS.forEach(entry => {
                    console.warn(`      - ${this.getFileName(entry.name)}`);
                });
            } else {
                console.log('   ✅ No render-blocking CSS detected');
            }
        }
    }

    generateReport() {
        const report = {
            timestamp: new Date().toISOString(),
            criticalCSS: {
                size: this.criticalCSSSize,
                sizeFormatted: this.formatBytes(this.criticalCSSSize),
                isOptimal: this.criticalCSSSize <= 14000
            },
            asyncLoading: {
                cssLoaderAvailable: !!window.cssLoader,
                loadedStylesheets: window.cssLoader ? window.cssLoader.loadedStyles.size : 0
            },
            performance: {
                loadTimes: this.loadTimes,
                totalCSSFiles: Object.keys(this.loadTimes).length
            },
            recommendations: this.getRecommendations()
        };

        // Store report for debugging
        window.cssOptimizationReport = report;
        
        console.log('📊 CSS Optimization Report:');
        console.table(report.criticalCSS);
        console.table(report.asyncLoading);
        
        if (report.recommendations.length > 0) {
            console.log('💡 Recommendations:');
            report.recommendations.forEach(rec => console.log(`   ${rec}`));
        }

        return report;
    }

    getRecommendations() {
        const recommendations = [];

        if (this.criticalCSSSize > 14000) {
            recommendations.push('🔧 Reduce critical CSS size to under 14KB');
        }

        if (!window.cssLoader) {
            recommendations.push('🔧 Implement CSS loader for async loading');
        }

        const slowCSS = Object.entries(this.loadTimes).filter(([_, time]) => time > 1000);
        if (slowCSS.length > 0) {
            recommendations.push('🔧 Optimize slow-loading CSS files');
        }

        if (recommendations.length === 0) {
            recommendations.push('✅ CSS optimization looks good!');
        }

        return recommendations;
    }

    formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    getFileName(url) {
        return url.split('/').pop().split('?')[0];
    }

    // Manual validation methods
    static validateCriticalCSS() {
        const validator = new CSSValidator();
        validator.checkCriticalCSS();
    }

    static validateAsyncLoading() {
        const validator = new CSSValidator();
        validator.checkAsyncLoading();
    }

    static generateFullReport() {
        const validator = new CSSValidator();
        return validator.generateReport();
    }

    // Performance monitoring
    static monitorCSSLoading() {
        const observer = new PerformanceObserver((list) => {
            list.getEntries().forEach((entry) => {
                if (entry.name.includes('.css')) {
                    console.log(`🎨 CSS loaded: ${entry.name} (${entry.duration.toFixed(2)}ms)`);
                }
            });
        });
        
        observer.observe({ entryTypes: ['resource'] });
    }

    // Test async loading
    static testAsyncLoading(cssUrl) {
        if (!window.cssLoader) {
            console.error('CSS loader not available');
            return;
        }

        const startTime = performance.now();
        
        window.cssLoader.loadStylesheet(cssUrl)
            .then(() => {
                const endTime = performance.now();
                console.log(`✅ Async CSS loaded: ${cssUrl} (${(endTime - startTime).toFixed(2)}ms)`);
            })
            .catch((error) => {
                console.error(`❌ Failed to load CSS: ${cssUrl}`, error);
            });
    }
}

// Auto-initialize in development mode
if (window.location.hostname === 'localhost' || window.location.hostname.includes('dev')) {
    new CSSValidator();
}

// Make available globally for manual testing
window.CSSValidator = CSSValidator;

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CSSValidator;
}
