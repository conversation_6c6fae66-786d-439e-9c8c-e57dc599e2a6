window.addEventListener("load", async (event) => {
    function storeQueryParamsInLocalStorage() {
        const allowedKeys = [
            "a",
            "utm_source",
            "utm_medium",
            "utm_campaign",
            "utm_term",
            "utm_content",
            "pxl",
            "zoneid",
            "aff_id",
            "querystring",
            "source",
            "referer_domain",
        ];

        const affParams = new URLSearchParams(window.location.search);
        const existingData =
            JSON.parse(localStorage.getItem("affParams")) || {};
        const now = Date.now();
        const TIME_TO_LIVE = 12 * 60 * 60 * 1000; // 12 hours in milliseconds

        // Determine if the existing data is expired
        const timestamp = Number(existingData._timestamp);
        const isExpired = !timestamp || ((now - timestamp) > TIME_TO_LIVE);


        // Create new object if expired, otherwise keep existing
        let storedParams = isExpired ? {} : { ...existingData };

        let hasNewSource = false;
        let hasQueryParams = false;

        affParams.forEach((value, key) => {
            if (allowedKeys.includes(key)) {
                hasQueryParams = true;
                if (key === "a" || key === "aff_id") {
                    storedParams["aff_id"] = value;
                }
                if (key === "source" || key === "referer_domain") {
                    storedParams["source"] = value;
                    hasNewSource = true;
                }
                storedParams[key] = value;
            }
        });

        // Fallback to document.referrer only if it's from a different domain
        if (
            !hasNewSource &&
            (!storedParams.source || isExpired) &&
            document.referrer
        ) {
            try {
                const refDomain = new URL(document.referrer).hostname;
                const currentDomain = window.location.hostname;

                if (refDomain && refDomain !== currentDomain) {
                    storedParams["source"] = refDomain;
                }
            } catch (e) {
                // Ignore invalid referrer URL
            }
        }

        // Only update localStorage if:
        // - data is expired, or
        // - new query params are present
        if (isExpired || hasQueryParams || hasNewSource) {
            storedParams["_timestamp"] = now;
            localStorage.setItem("affParams", JSON.stringify(storedParams));
        }
    }

    storeQueryParamsInLocalStorage();
});
