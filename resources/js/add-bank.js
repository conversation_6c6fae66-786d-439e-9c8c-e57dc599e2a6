
const handleAddBank = () => {
    const form = $('#add_banking_submit');
    const buttonSubmit = form.find('.button-submit');
    const dropdownItems = form.find('.dropdown-item');

    dropdownItems.each(function () {
        $(this).on('click', function (event) {
            checkShowSubmit(form, buttonSubmit);
        })
    })

    form.validate({
        rules: {
            bank_code: { required: true },
            bank_account_name: { required: true },
            bank_account_no: { required: true },
        },
        errorPlacement: function (error, element) {
            return '';
        },
        onkeyup: function () {
            checkShowSubmit(form, buttonSubmit);
        },
        submitHandler: function (form) {
            handleSubmitForm(form);
        },
    });

    const handleSubmitForm = async (form) => {
        const formEl = $(form);
        const data = formEl.serializeArray();
        const payload = {};

        data.forEach((item) => payload[item.name] = item.value);

        const res = await submitData('/account/createBank', payload, '', '');

        if (res.status === 'OK') {
            useToast('success', res?.message);
            setTimeout(() => window.location.href = '/account/withdraw/bank?type=add-bank', 1500);
        } else {
            useToast('error', res?.message);
        }
    }
}

window.handleAddBank = handleAddBank;
