window.initVerifyEmailForm = () => {
    // list error message
    const requiredEmailMsg = $('#required_email_error').text();
    const invalidEmailMsg = $('#invalid_email_error').text();
    const requiredOtpMsg = $('#required_otp_error').text();

    const emailForm = $("#verify-account-email");
    
    const mailSendBtn = emailForm.find("#mail-send-button");
    const emailInput = emailForm.find("#email");
    let emailValue = emailInput.val().trim();

    let countdownInterval;
    const otpForm = $("#verify-account-otp");
    const otpSendBtn = $("#otp-send-button");
    const otpInput = otpForm.find("#otp");
    const countdown = $('#countdown');
    const resendOtp = $('#resend-otp')

    // Thêm custom method cho email pattern
    $.validator.addMethod(
        "emailPattern",
        function(value, element) {
            return /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(value);
        },
        invalidEmailMsg
    );

    emailForm.validate({
        rules: {
            email: {
                required: true,
                email: true,
                emailPattern: true  // Sử dụng custom method
            },
        },
        messages: {
            email: {
                required: requiredEmailMsg,
                email: invalidEmailMsg,
                emailPattern: invalidEmailMsg
            },
        },

        errorClass: "error",
        errorPlacement: function (error, element) {
            validationField(element, 'error', error);
        },
        success: function(label) {
            validationField(label, 'success');
        },
        highlight: function(element) {
            validationField(element, 'error', '');
        },
        submitHandler: function(form, event) {
            event.preventDefault();
        }
    });

    otpForm.validate({
        rules: {
            otp :{
                required: true, 
                minlength: 6,
            },
        },
        messages:{
            otp:{
                required: requiredOtpMsg,
                minlength: 'Mã kích hoạt phải có 6 ký tự'
            },
        },

        errorClass: "error",
        errorPlacement: function (error, element) {
            validationField(element, 'error', error);
        },
        success: function(label) {
            validationField(label, 'success');
        },
        highlight: function(element) {
            validationField(element, 'error', '');
        },
        submitHandler: function(form, event) {
            event.preventDefault();
        }
    });

    emailForm.find('input').on('input', function() {
        checkShowSubmit(emailForm, mailSendBtn);
    });

    otpForm.find('input').on('input', function() {
        checkShowSubmit(otpForm, otpSendBtn);
    });

    const startCountdown = (duration) => {
        let timeLeft = duration;
        countdown.text(` (${timeLeft}s)`);
        countdownInterval = setInterval(() => {
            timeLeft -= 1;
            if (timeLeft > 0) {
                countdown.text(` (${timeLeft}s)`);
                resendOtp.prop('disabled',true).addClass('cursor-not-allowed');
                $('.js-popup-button-confirm').prop('disabled',true).text(`Gửi Lại Mã (${timeLeft}s)`);
            } else {
                clearInterval(countdownInterval);
                countdown.text('');
                resendOtp.prop('disabled',false).removeClass('cursor-not-allowed');
                $('.js-popup-button-confirm').prop('disabled',false).text('Gửi Lại Mã');
            }
        }, 1000);
    };

    function encodeEmail(email) {
        const [localPart, domain] = email.split('@');
        const maskedLocalPart = localPart.slice(0, 6) + '***';
        return `${maskedLocalPart}@${domain}`;
    }

    async function sendMail(e) {
        $('.js-popup-button-confirm').prop('disabled',true).text(`Gửi Lại Mã`);
        clearInterval(countdownInterval);
        if (!emailForm.validate().checkForm()) {
            e.preventDefault();
        } else {
            emailValue = emailInput.val().trim();
            const params = {
                email: emailInput.val().trim()
            }
            mailSendBtn.prop('disabled',false);
            const res = await fetchData('/user/getOTP', params, {}, '');
            mailSendBtn.prop('disabled',false);
            if (res.code === 200 && res.status === 'OK') {
                $('#account-info-email').val(encodeEmail(params.email));
                emailForm.addClass('hidden');
                otpForm.removeClass('hidden');
                startCountdown(60);
                useToast('success', res?.message);
            } else {
                $(emailInput).closest('.input-container').addClass('error-validate');
                $(emailInput).closest('.input-container').find('.input-error').removeClass('hidden');
                $(emailInput).closest('.input-container').find('.input-error').text(res?.message);
            }
        }
    }

    async function reVerifyOTP() {
        setTimeout(() =>  sendMail(),0)
    }

    async function verifyOTP(e){
        if (!emailForm.validate().checkForm()) {
            e.preventDefault();
        } else {
            const params = {
                code: otpInput.val().trim()
            };

            const res = await submitData('/user/emailOTPVerification', params, '');
            
            if (res?.status !== 'OK') {
                
                openNotiModal(
                    'THÔNG BÁO',
                    'Mã OTP không đúng, vui lòng thử lại.', 
                    'Đóng', 
                    'Gửi lại mã', 
                    '/asset/images/popup/img-notification-error.avif', 
                    ()=>{}, 
                    reVerifyOTP, 
                    "",
                    false, 
                    ()=>{}, 
                    false,
                    false
                );

            } else if (res?.status === 'OK' && res?.code === 200) {
                useToast('success', 'Xác minh Email thành công!');
                await fetchData("/refresh", {}, {}, "", "");
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            }
        }
    }

    //send mail
    mailSendBtn.on('click', debounce(sendMail));

    //resend mail otp
    resendOtp.on('click', debounce(sendMail));

    //send otp
    otpSendBtn.on('click', debounce(verifyOTP));
}

document.addEventListener("DOMContentLoaded", async () => {
    const res = await fetchData("/refresh", {}, {}, "", "");
    
    if (res.status == "OK") {
        $('#account-info-email').val(res.user.email);
    }

    const changeNameButton = $('.js-change-name');

    if (changeNameButton) {
        changeNameButton.on('click', function () {
            openChangeName();
        })
    }
})
