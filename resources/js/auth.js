import "./input";

let siteKey3 = import.meta.env.VITE_RECAPTCHA_V3_SITE_KEY || '';
let siteKey2 = import.meta.env.VITE_RECAPTCHA_V2_SITE_KEY || '';

let recaptchaV3Loaded = false
let v2WidgetId = null;
let v2Rendered = false;
let validV2 = true;
let isRecaptchaLoaded = false;

const loadScript = (src, callback) => {
    if (document.querySelector(`script[src="${src}"]`)) {
        callback?.();
        return;
    }
    const script = document.createElement('script');

    script.src = src;
    script.async = true;
    script.defer = true;
    script.onload = () => callback?.();
    script.onerror = () => callback?.(new Error('Script load error'));
    document.head.appendChild(script);
};

const loadRecaptchaV3 = () => {
    return new Promise((resolve, reject) => {
        if (!siteKey3) return resolve('');

        loadScript(`https://www.google.com/recaptcha/api.js?render=${siteKey3}`, (err) => {
            if (err) {
                return reject(new Error('Failed to load reCAPTCHA script'));
            }

            if (typeof grecaptcha === 'undefined' || typeof grecaptcha.ready !== 'function') {
                return reject(new Error('grecaptcha not available after script load'));
            }

            try {
                grecaptcha.ready(() => {
                    if (typeof grecaptcha.execute !== 'function') {
                        return reject(new Error('grecaptcha.execute not available'));
                    }

                    grecaptcha.execute(siteKey3, { action: 'submit' })
                        .then(token => resolve(token))
                        .catch(err => {
                            reject(new Error('Failed to execute reCAPTCHA v3'));
                        });
                });
            } catch (e) {
                reject(new Error('Unexpected error while executing reCAPTCHA'));
            }
        });
    });
};

const loadRecaptchaV2 = () => {
    return new Promise((resolve, reject) => {
        const containerId = 'recaptcha-v2-container';

        loadScript('https://www.google.com/recaptcha/api.js', (err) => {
            setTimeout(() => {
                if (err) {
                    return reject(new Error('Failed to load reCAPTCHA script'));
                }

                if (typeof grecaptcha === 'undefined' || typeof grecaptcha.ready !== 'function') {
                    return reject(new Error('grecaptcha not available after script load'));
                }

                try {
                    if (!v2Rendered || !document.getElementById(containerId) || !document.querySelector(`#${containerId} iframe`)) {
                        v2WidgetId = grecaptcha.render(containerId, {
                            sitekey: siteKey2,
                            callback: (token) => {
                                const inputToken = $('#recaptcha-token');
                                $('.recaptcha-checkbox').prop('checked', true).trigger('change');
                                setTimeout(()=>{
                                    const form = $(".signup-form").find("#signup-form-modal");
                                    const valid = form.validate().checkForm();
                                    if(valid) {
                                        $(form).trigger('submit');
                                    }
                                },100)
                                inputToken.val(token);
                                resolve(token);
                            },
                            'expired-callback': () => {
                                $('#recaptcha-token').val('');
                                $('.recaptcha-checkbox').prop('checked', false);
                            },
                            'error-callback': (e) => {
                                $('.recaptcha-checkbox').prop('checked', false);
                                const inputToken = $('#recaptcha-token');
                                validV2 = false;
                                siteKey2 = "";
                                $('#recaptcha-v2-container').removeClass('flex').addClass('hidden');
                                $('.recaptcha-checkbox').prop('checked', true).trigger('change');
                                inputToken.val('bypass');
                                reject(new Error('reCAPTCHA v2 render error'));
                            }
                        });

                        setTimeout(() => {
                            if (validV2) {
                                resolve();
                            }
                        }, 1500);

                        v2Rendered = true;
                    } else {
                        grecaptcha.reset(v2WidgetId);
                        resolve();
                    }
                } catch (e) {
                    if (/client element has been removed/.test(e.message)) {
                        v2Rendered = false;
                        v2WidgetId = null;
                        loadRecaptchaV2().then(resolve).catch(reject);
                    } else {
                        reject(e);
                    }
                }
            }, 1000);
        });
    });
};

const handleRecaptcha = async () => {
    const inputToken = $('#recaptcha-token');
    $('.recaptcha-checkbox').prop('checked', false).trigger('change');

    if (!inputToken.length) {
        $('.recaptcha-checkbox').prop('checked', true).trigger('change');
        return 'bypass';                
    } 

    if (!siteKey3 && !siteKey2) {
        inputToken.val('bypass');
        $('.recaptcha-checkbox').prop('checked', true).trigger('change');
        return 'bypass';
    }

    try {
        const token = await loadRecaptchaV3();

        if (token) {
            $('.recaptcha-checkbox').prop('checked', true).trigger('change');
            inputToken.val(token);
            return token;
        }

        throw new Error('No token returned from v3');
    } catch (err) {
        siteKey3 = "";
        
        try {
            await loadRecaptchaV2();
        } catch (err2) {
            validV2 = false;
            siteKey2 = "";
            $('#recaptcha-v2-container').removeClass('flex').addClass('hidden');
            $('.recaptcha-checkbox').prop('checked', true).trigger('change');
            inputToken.val('bypass');
        } finally {
            if (validV2) {
                $('#recaptcha-v2-container').addClass('flex').removeClass('hidden');
            }
        }
    }
};

const lazyLoadRecaptchaOnSubmit = async () => {
    if(!recaptchaV3Loaded){
        recaptchaV3Loaded = true;
        try {
            await handleRecaptcha();
        } catch (err) {
            console.warn('Initial reCAPTCHA failed:', err);
        }
    }
};

window.handleRecaptcha = handleRecaptcha;

const clearAllCache = async () => {
    const cacheNames = await caches.keys();
    for (const name of cacheNames) {
        await caches.delete(name);
    }
};

function bindLoginForm(redirect = null) {
    const loginForm = $(".login-form");

    if (loginForm) {
        const buttonSubmit = loginForm.find(".button-submit");
        const form = loginForm.find("#login-form-modal");
        const inputFields = loginForm.find(".input-field");

        form.validate({
            rules: {
                username: {
                    required: true,
                    alphanumeric: true,
                    minlength: 6,
                    maxlength: 29,
                },
                pwd: {
                    required: true,
                    noSpace: true,
                    minlength: 6,
                    maxlength: 32,
                },
            },
            messages: {
                username: {
                    required: "Vui lòng nhập tên đăng nhập",
                    minlength: "Tên đăng nhập từ 6 đến 29 ký tự",
                    maxlength: "Tên đăng nhập từ 6 đến 29 ký tự",
                },
                pwd: {
                    required: "Vui lòng nhập mật khẩu",
                    minlength: "Mật khẩu từ 6 đến 32 ký tự",
                    maxlength: "Mật khẩu từ 6 đến 32 ký tự",
                },
            },
            errorClass: "error",
            errorPlacement: function (error, element) {
                validationField(element, "error", error);
            },
            success: function (label) {
                validationField(label, "success");
            },
            highlight: function (element) {
                validationField(element, "error", "");
            },
            submitHandler: function (form, e) {
                e.preventDefault();
                // Remove focus from the form
                $(form).find("input:focus").blur();
                handleSubmitForm(form);
            },
        });

        inputFields.each(function () {
            $(this).on("change input", function () {
                checkShowSubmit(form, buttonSubmit);
                $(this).valid();
            });
            if ($(this).val()) {
                $(this).trigger("change");
                $(this).trigger("input");
            }
        });

        const openErrorLoginModal = (message = "") => {
            let displayModal = errorLoginModal;
            switch (message) {
                case "Không tìm thấy người dùng.":
                    displayModal = errorLoginNotfoundModal;
                    break;
                case "Tài khoản của bạn đã bị khoá, vui lòng liên hệ support để được giải quyết.":
                    displayModal = errorLoginBlockModal;
                    break;
                default:
                    break;
            }
            openModal(displayModal, false, "error-login-modal");
            $("#error-login-text").text(message);
        };

        const handleSubmitForm = async (form) => {
            const formEl = $(form);
            const data = formEl.serializeArray();
            const payload = {};

            data.forEach((item) => {
                if (item.name === 'pwd') {
                    payload['password'] = item.value
                } else {
                    payload[item.name] = item.value
                }
            });

            try {
                buttonSubmit.attr("disabled", "");
                const inputToken = $('#recaptcha-token');

                if (inputToken.val() !== 'bypass') {
                    payload.token = inputToken.val();
                }

                // Check affiliate params
                const affParams =
                    JSON.parse(localStorage.getItem("affParams")) || {};
                if (affParams) {
                    Object.assign(payload, affParams);
                }
                if (window.dataLayer && Array.isArray(window.dataLayer)) {
                    window.dataLayer.push({ event: "formSubmitted", formName: "Form_Login" });
                }
                const res = await submitData("/login", payload, "", "");
                const dataGameOpen = sessionStorage.getItem('dataGameOpen');
                const dataGameHeaderOpen = sessionStorage.getItem('dataGameHeaderOpen');
                const dataGameMobileOpen = sessionStorage.getItem('dataGameMobileOpen');
                const openMiniGameAfterAuth = sessionStorage.getItem('openMiniGameAfterAuth');
                const authForQuickbet = sessionStorage.getItem('authForQuickbet');

                if (res.status) {
                    useToast("success", res?.message);
                    await clearAllCache();

                    if (dataGameOpen || dataGameHeaderOpen || dataGameMobileOpen || authForQuickbet) {
                        if (res?.data?.is_updated_fullname == 0) {
                            $('.js-header-auth').addClass('flex').removeClass('hidden');
                            $('.js-header-login').addClass('hidden').removeClass('block');
                            $('.js-fullname-account').text(res?.data?.fullname ?? res?.data?.username);
                            $('.js-account-balance').text(res?.data?.balance_txt ?? 0 + ' K');

                            closeAuthModal();
                            sessionStorage.setItem('changeNameAfterLogin', true);

                            if (dataGameOpen) {
                                await openGame(JSON.parse(dataGameOpen));
                            } {
                                await openChangeName();
                            }
                        } else {
                            if (dataGameOpen) {
                                await openGame(JSON.parse(dataGameOpen));
                                handleReloadAllPage();
                            } else if (dataGameHeaderOpen) {
                                await openGameHeaderItem(JSON.parse(dataGameHeaderOpen));
                            } else if (dataGameMobileOpen) {
                                sessionStorage.removeItem('dataGameMobileOpen');
                                window.open(dataGameMobileOpen, '_blank');
                                handleReloadAllPage();
                            } else if (authForQuickbet) {
                                const quickbetContainer = $('.quickbet-container');
                                const quickbetCanvas = $('.quickbet-canvas');

                                $('.js-header-auth').addClass('flex').removeClass('hidden');
                                $('.js-header-login').addClass('hidden').removeClass('block');
                                $('.js-fullname-account').text(res?.data?.fullname ?? res?.data?.username);
                                $('.js-account-balance').text(res?.data?.balance_txt ?? 0 + ' K');
                                sessionStorage.removeItem('authForQuickbet');
                                closeAuthModal();
                                quickbetCanvas.remove();
                                $('#playerWrapper-pixi-widget-xocdia-77786').remove();
                                quickbetContainer.append('<canvas class="quickbet-canvas" id="pixi-widget-xocdia-77786" style="touch-action: none; cursor: inherit;"></canvas>')
                                window.handleRenderQuickbet(res?.data?.tp_token);
                            }
                        }
                    } else {
                        if (openMiniGameAfterAuth) {
                                $('#minigame-container').addClass('hidden').removeClass('block');
                                $('c2-minigame').attr('token', res?.data?.tp_token);
                                $('c2-minigame').addClass('block').removeClass('hidden');
                                $('.js-header-auth').addClass('flex').removeClass('hidden');
                                $('.js-header-login').addClass('hidden').removeClass('block');
                                $('.js-fullname-account').text(res?.data?.fullname ?? res?.data?.username);
                                $('.js-account-balance').text(res?.data?.balance_txt ?? 0 + ' K');
                                window.forceRerender();
                        } else {
                            if (redirect) {
                                setTimeout(
                                    () => window.location.replace(redirect),
                                    500
                                );
                            } else {
                                if (screen.width < 1200) {
                                    window.scrollTo({ top: 0, behavior: "smooth" });
                                }

                                handleReloadAllPage();
                            }
                        }
                    }
                } else {
                    buttonSubmit.removeAttr("disabled");
                    openErrorLoginModal(res?.message);
                }
            } catch (error) {
                openErrorLoginModal(res?.message);
                buttonSubmit.removeAttr("disabled");
            }
        };
    }
}

function bindSignupForm() {
    const signupForm = $(".signup-form");
    recaptchaV3Loaded = false;
    v2WidgetId = null;
    v2Rendered = false;
    validV2 = true;

    if (signupForm) {
        const buttonSubmit = signupForm.find(".button-submit");
        const form = signupForm.find("#signup-form-modal");
        const inputFields = signupForm.find(".input-field");

        form.validate({
            rules: {
                username: {
                    required: true,
                    alphanumeric: true,
                    minlength: 6,
                },
                phone: {
                    required: true,
                    mobileValidation: true,
                    minlength: 10,
                },
                pwd: {
                    required: true,
                    noSpace: true,
                    minlength: 6,
                    maxlength: 32,
                },
                // recaptcha_checkbox: {
                //     required: true
                // }
            },
            messages: {
                username: {
                    required: "Vui lòng nhập tên đăng nhập",
                    minlength: "Tên đăng nhập từ 6 đến 29 ký tự",
                },
                phone: {
                    required: "Vui lòng nhập số điện thoại",
                    minlength: "Nhập ít nhất 10 chữ số",
                },
                pwd: {
                    required: "Vui lòng nhập mật khẩu",
                    minlength: "Mật khẩu từ 6 đến 32 ký tự",
                    maxlength: "Mật khẩu từ 6 đến 32 ký tự",
                },
            },
            errorClass: "error",
            errorPlacement: function (error, element) {
                validationField(element, "error", error);
            },
            success: function (label) {
                validationField(label, "success");
            },
            highlight: function (element) {
                validationField(element, "error", "");
            },
            submitHandler: function (form, e) {
                e.preventDefault();
                // Remove focus from the form
                $(form).find("input:focus").blur();
                handleSubmitForm(form);
            },
        });

        inputFields.each(function () {
            $(this).on("change input", function () {
                checkShowSubmit(form, buttonSubmit);
                $(this).valid();
            });
            if ($(this).val()) {
                $(this).trigger("change");
                $(this).trigger("input");
            }
        });

        const handleSubmitForm = async (form) => {
            if (window.isOnline) {
                if(!isRecaptchaLoaded && $('#recaptcha-token')!=='bypass') {
                    buttonSubmit.attr("disabled", "");
                    lazyLoadRecaptchaOnSubmit();
                    isRecaptchaLoaded = true;
                    return;
                }

                const formEl = $(form);
                const data = formEl.serializeArray();
                const payload = {};
                const validName = ['pwd', 'username', 'phone'];

                data.forEach((item) => {
                    if(validName?.includes(item?.name)) {
                        if (item.name === 'pwd') {
                            payload['password'] = item.value
                        } else {
                            payload[item.name] = item.value
                        }
                    }
                });

                if (siteKey3 || siteKey2) {
                    if (siteKey2 && !siteKey3) {
                        payload['version'] = 'v2'
                    } else if (siteKey3) {
                        payload['version'] = 'v3'
                    }
                }

                payload["confirmPassword"] = payload["password"];

                try {
                    buttonSubmit.attr("disabled", "");

                    const inputToken = $('#recaptcha-token');

                    if (inputToken.val() !== 'bypass') {
                        payload.token = inputToken.val();
                    }

                    const affParams =
                        JSON.parse(localStorage.getItem("affParams")) || {};
                    if (affParams) {
                        Object.assign(payload, affParams);
                    }
                    if (window.dataLayer && Array.isArray(window.dataLayer)) {
                        window.dataLayer.push({ event: "formSubmitted", formName: "Form\_Register" });
                    }
                    const res = await submitData("/register", payload, "", "");
                    const dataGameOpen = sessionStorage.getItem('dataGameOpen');
                    const dataGameHeaderOpen = sessionStorage.getItem('dataGameHeaderOpen');
                    const dataGameMobileOpen = sessionStorage.getItem('dataGameMobileOpen');
                    const openMiniGameAfterAuth = sessionStorage.getItem('openMiniGameAfterAuth');
                    const authForQuickbet = sessionStorage.getItem('authForQuickbet');

                    if (res?.status) {
                        useToast("success", "Đăng ký tài khoản thành công");
                        await clearAllCache();

                        if (dataGameOpen || dataGameHeaderOpen || dataGameMobileOpen || authForQuickbet) {
                            $('.js-header-auth').addClass('flex').removeClass('hidden');
                            $('.js-header-login').addClass('hidden').removeClass('block');
                            $('.js-fullname-account').text(res?.data?.fullname ?? res?.data?.username);
                            $('.js-account-balance').text(res?.data?.balance_txt ?? 0 + ' K');

                            if (authForQuickbet) {
                                const quickbetContainer = $('.quickbet-container');
                                const quickbetCanvas = $('.quickbet-canvas');

                                quickbetCanvas.remove();
                                $('#playerWrapper-pixi-widget-xocdia-77786').remove();
                                quickbetContainer.append('<canvas class="quickbet-canvas" id="pixi-widget-xocdia-77786" style="touch-action: none; cursor: inherit;"></canvas>')
                                window.handleRenderQuickbet(res?.data?.tp_token);
                            }

                            closeAuthModal();
                            sessionStorage.setItem('changeNameAfterLogin', true);
                            await openChangeName();
                        } else {
                            if (openMiniGameAfterAuth) {
                                $('#minigame-container').addClass('hidden').removeClass('block');
                                $('c2-minigame').attr('token', res?.data?.tp_token);
                                $('c2-minigame').addClass('block').removeClass('hidden');
                                $('.js-header-auth').addClass('flex').removeClass('hidden');
                                $('.js-header-login').addClass('hidden').removeClass('block');
                                $('.js-fullname-account').text(res?.data?.fullname ?? res?.data?.username);
                                $('.js-account-balance').text(res?.data?.balance_txt ?? 0 + ' K');
                                window.forceRerender();
                            } else {
                                if (screen.width < 1200) {
                                    window.scrollTo({ top: 0, behavior: "smooth" });
                                }

                                handleReloadAllPage();
                            }
                        }
                    } else {
                        if (res?.code === 202 && res?.fallback) {
                            if (siteKey3 || siteKey2) {
                                if (siteKey2 && !siteKey3) {
                                    siteKey2 = "";
                                    $('#recaptcha-v2-container').html('');
                                    v2WidgetId = null;
                                    v2Rendered = false;
                                } else if (siteKey3) {
                                    siteKey3 = "";
                                }
                            }
                        }

                        buttonSubmit.removeAttr("disabled");
                        useToast("error", res?.message);
                        await handleRecaptcha();
                    }
                } catch (error) {
                    useToast("error", error?.message);
                    buttonSubmit.removeAttr("disabled");
                    await handleRecaptcha();
                }
            } else {
                useToast("error", 'Kết nối mạng không ổn định, vui lòng thử lại sau!');
            }
       
        };
    }
}

function bindForgetForm() {
    const forgetForm = $(".forget-form");
    const isSet = forgetForm.attr("isSet");

    if (typeof isSet === "undefined" || isSet === false) {
        forgetForm.attr("isSet", "");
        const buttonSubmit = forgetForm.find(".button-submit");
        const form = forgetForm.find("#forget-form-modal");
        const inputFields = forgetForm.find(".input-field");

        form.validate({
            rules: {
                email: {
                    required: true,
                    emailPattern: true,
                },
            },
            messages: {
                email: {
                    required: "Vui lòng nhập email",
                    email: "Địa chỉ email không hợp lệ",
                    emailPattern: "Địa chỉ email không hợp lệ",
                },
            },
            errorClass: "error",
            errorPlacement: function (error, element) {
                validationField(element, "error", error);
            },
            success: function (label) {
                validationField(label, "success");
            },
            highlight: function (element) {
                validationField(element, "error", "");
            },
            submitHandler: function (form, e) {
                e.preventDefault();
                // Remove focus from the form
                $(form).find("input:focus").blur();
                handleSubmitForm(form);
            },
        });

        inputFields.each(function () {
            $(this).on("change input", function () {
                checkShowSubmit(form, buttonSubmit);
                $(this).valid();
            });
            if ($(this).val()) {
                $(this).trigger("change");
                $(this).trigger("input");
            }
        });

        const handleSubmitForm = async (form) => {
            const formEl = $(form);
            const data = formEl.serializeArray();
            const payload = {};

            data.forEach((item) => (payload[item.name] = item.value));

            try {
                buttonSubmit.attr("disabled", "");
                const res = await fetchData("/user/sendOtp", payload, "", "");

                if (res?.code === 200) {
                    if (res?.status === "OK") {
                        useToast("success", res?.message);
                        openAuthModal(newPassModal, true, "new-pass-modal");
                        handleInput();

                        if (payload["email"]) {
                            $(".new-pass-form").attr("email", payload["email"]);
                        } else {
                            $(".new-pass-form").attr(
                                "username",
                                payload["username"]
                            );
                        }
                        bindChangePassForm();
                    } else {
                        useToast("error", res?.message);
                    }
                }
            } catch (error) {
                useToast("error", error?.message);
            } finally {
                buttonSubmit.removeAttr("disabled");
            }
        };
    }
}

function bindChangePassForm() {
    const changePassForm = $(".new-pass-form");
    const isSet = changePassForm.attr("isSet");
    const email = changePassForm.attr("email");
    const username = changePassForm.attr("username");

    if (typeof isSet === "undefined" || isSet === false) {
        changePassForm.attr("isSet", "");
        const inputFields = changePassForm.find(".input-field");
        const form = changePassForm.find("#new-pass-form-modal");
        const buttonSubmit = changePassForm.find(".button-submit");
        const buttonSendAgain = changePassForm.find(".button-send-again");

        const inputEmail = changePassForm.find(".input-email");
        const inputUserName = changePassForm.find(".input-username");
        const inputPassword = changePassForm.find(".input-password");

        if (email) {
            inputEmail.val(email);
        } else {
            inputUserName.val(username);
        }

        buttonSendAgain.on("click", async function () {
            const payload = {};

            if (email) {
                payload["email"] = email;
            } else {
                payload["username"] = username;
            }

            const res = await fetchData("/user/sendOtp", payload, "", "");

            if (res?.code === 200) {
                if (res?.status !== "VALIDATE_FAILED") {
                    useToast("success", res?.message);
                } else {
                    useToast("error", res?.message);
                }
            }
        });

        form.validate({
            rules: {
                otp: {
                    required: true,
                    minlength: 6,
                    maxlength: 6,
                },
                password: {
                    required: true,
                    noSpace: true,
                    minlength: 6,
                    maxlength: 32,
                },
                confirmPassword: {
                    required: true,
                    noSpace: true,
                    equalTo: inputPassword.find(".input-field"),
                },
            },
            messages: {
                otp: {
                    required: "Vui lòng nhập mã kích hoạt",
                    minlength: "Mã kích hoạt phải có 6 ký tự",
                    maxlength: "Mã kích hoạt phải có 6 ký tự",
                },
                password: {
                    required: "Vui lòng nhập mật khẩu",
                    minlength: "Mật khẩu từ 6 đến 32 ký tự",
                    maxlength: "Mật khẩu từ 6 đến 32 ký tự",
                },
                confirmPassword: {
                    required: "Vui lòng nhập xác nhận mât khẩu",
                    equalTo: "Mật khẩu mới không trùng khớp",
                },
            },
            errorClass: "error",
            errorPlacement: function (error, element) {
                validationField(element, "error", error);
            },
            success: function (label) {
                validationField(label, "success");
            },
            highlight: function (element) {
                validationField(element, "error", "");
            },
            submitHandler: function (form, e) {
                e.preventDefault();
                // Remove focus from the form
                $(form).find("input:focus").blur();
                handleSubmitForm(form);
            },
        });

        inputFields.each(function () {
            $(this).on("change input", function () {
                checkShowSubmit(form, buttonSubmit);
                $(this).valid();
            });
            if ($(this).val()) {
                $(this).trigger("change");
                $(this).trigger("input");
            }
        });

        const handleSubmitForm = async (form) => {
            const formEl = $(form);
            const data = formEl.serializeArray();
            const payload = {};

            data.forEach((item) => (payload[item.name] = item.value));

            try {
                buttonSubmit.attr("disabled", "");
                const res = await submitData("/change-pass", payload, "", "");

                if (res?.status === "ERROR" || !res?.status) {
                    useToast("error", res?.message);
                } else {
                    buttonSubmit.removeAttr("disabled");
                    useToast("success", "Cập nhật mật khẩu thành công");
                    closeAuthModal();
                    openLogin();
                }
            } catch (error) {
                buttonSubmit.removeAttr("disabled");
                useToast("error", error?.message);
            }
        };
    }
}

function bindChangeNameForm() {
    const form = $("#change-name-form-modal");

    if (form) {
        const inputFields = form.find(".input-field");
        const buttonSubmit = form.find(".button-submit");

        form.validate({
            rules: {
                fullname: {
                    required: true,
                    minlength: 6,
                    alphanumeric: true,
                    maxlength: 29,
                },
            },
            messages: {
                fullname: {
                    required: "Vui lòng nhập tên hiển thị",
                    minlength: "Tên hiển thị từ 6 đến 29 ký tự",
                    alphanumeric: "Tên hiển thị chỉ được gồm: a-z, 0-9",
                },
            },
            errorClass: "error",
            errorPlacement: function (error, element) {
                validationField(element, "error", error);
            },
            success: function (label) {
                validationField(label, "success");
            },
            highlight: function (element) {
                validationField(element, "error", "");
            },
            submitHandler: function (form, e) {
                e.preventDefault();
                // Remove focus from the form
                $(form).find("input:focus").blur();
                handleSubmitForm(form);
            },
        });

        inputFields.each(function () {
            $(this).on("change input", function () {
                checkShowSubmit(form, buttonSubmit);
                $(this).valid();
            });
            if ($(this).val()) {
                $(this).trigger("change");
                $(this).trigger("input");
            }
        });

        const handleSubmitForm = async (form) => {
            const formEl = $(form);
            const data = formEl.serializeArray();
            const payload = {};

            data.forEach((item) => (payload[item.name] = item.value?.trim()));

            const dataGameOpen = sessionStorage.getItem('dataGameOpen');
            const dataGameSportOpen = sessionStorage.getItem('dataGameSportOpen');
            const dataGameHeaderOpen = sessionStorage.getItem('dataGameHeaderOpen');
            const dataGameMobileOpen = sessionStorage.getItem('dataGameMobileOpen');
            const dataGameMenuItemOpen = sessionStorage.getItem('dataGameMenuItemOpen');
            const authForQuickbet = sessionStorage.getItem('authForQuickbet');

            try {
                buttonSubmit.attr("disabled", "");
                const res = await submitData(
                    "/account/change-name",
                    payload,
                    "",
                    ""
                );
                if (res?.status === "OK") {
                    if (res?.user && res?.user?.fullname) {
                        useToast("success", res?.message ?? "Thành công");
                        $(".js-fullname-account").text(res?.user?.fullname);
                        await fetchData("/refresh", {}, {}, "", "");

                        if (dataGameOpen || dataGameSportOpen || dataGameHeaderOpen || dataGameMobileOpen || dataGameMenuItemOpen) {
                            if (dataGameOpen) {
                                sessionStorage.removeItem('dataGameOpen');
                                await openGame(JSON.parse(dataGameOpen), true);
                                handleReloadAllPage();
                            } else if (dataGameSportOpen) {
                                sessionStorage.removeItem('dataGameSportOpen');
                                await openSport(JSON.parse(dataGameSportOpen), true);

                                if (screen.width < 1200) {
                                    handleReloadAllPage();
                                }
                            } else if (dataGameHeaderOpen) {
                                sessionStorage.removeItem('dataGameHeaderOpen');
                                await openGameHeaderItem(JSON.parse(dataGameHeaderOpen), true);
                            } else if (dataGameMobileOpen) {
                                sessionStorage.removeItem('dataGameMobileOpen');
                                window.open(dataGameMobileOpen, '_blank');
                                handleReloadAllPage();
                            } else if (dataGameMenuItemOpen) {
                                sessionStorage.removeItem('dataGameMenuItemOpen');
                                await openGameMenuItem(dataGameMenuItemOpen, true);
                                handleReloadAllPage();
                            }

                            sessionStorage.setItem('changeNameSuccess', true);
                        } else {
                            if (window.detectClickMiniGame) {
                                closeModal();
                                window.detectClickMiniGame.trigger('click');
                                window.detectClickMiniGameBlur.trigger('click');
                                window.detectClickMiniGame = null;
                                window.detectClickMiniGameBlur = null;
                                $('.gamebox').addClass('!hidden');

                                setTimeout(() => {
                                    $('.gamebox').removeClass('!hidden');
                                }, 1000)
                            } else if (authForQuickbet) {
                                sessionStorage.removeItem('authForQuickbet');
                                closeModal();
                            } else {
                                handleReloadAllPage();
                            }
                        }
                    }
                } else {
                    useToast("error", res?.message);
                    buttonSubmit.removeAttr("disabled");
                }
            }   catch (error) {
                useToast("error", error?.message);
                buttonSubmit.removeAttr("disabled");
            }
        };
    }
}

// window.bindLoginForm = bindLoginForm;
// window.bindSignupForm = bindSignupForm;
// window.bindForgetForm = bindForgetForm;
// window.bindChangePassForm = bindChangePassForm;
// window.bindChangeNameForm = bindChangeNameForm;

const openChangeName = (isSticky = false) => {
    const dataGameOpen = sessionStorage.getItem('dataGameOpen');
    const changeNameAfterLogin = sessionStorage.getItem('changeNameAfterLogin');

    openModal(
        changeNameModal, 
        isSticky, 
        "change-name-modal", 
        false, 
        "", 
        () => {
            if (dataGameOpen && changeNameAfterLogin) {
                window.location.reload();
            }
        } 
    );
    bindChangeNameForm();
};

const openLogin = (redirect) => {
    openAuthModal(loginModal, true, "login-modal");
    handleInput();
    bindLoginForm(redirect);
};

const openSignup = () => {
    openAuthModal(signupModal, true, "signup-modal");
    handleInput();
    bindSignupForm();
};

const openForgetPass = () => {
    openAuthModal(forgetModal, true, "forget-modal");
    handleInput();
    bindForgetForm();
};

window.openChangeName = openChangeName;
window.openLogin = openLogin;
window.openSignup = openSignup;
window.openForgetPass = openForgetPass;

window.addEventListener('unhandledrejection', async function (event) {
    if (event?.reason?.message?.includes('Invalid site key')) {
        siteKey3 = '';
        await handleRecaptcha();
    }
});