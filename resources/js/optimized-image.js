// Optimized Image Loading with Intersection Observer
class OptimizedImageLoader {
    constructor() {
        this.imageObserver = null;
        this.init();
    }

    init() {
        // Check if Intersection Observer is supported
        if ('IntersectionObserver' in window) {
            this.setupIntersectionObserver();
        } else {
            // Fallback for older browsers
            this.loadAllImages();
        }
    }

    setupIntersectionObserver() {
        const options = {
            root: null,
            rootMargin: '50px',
            threshold: 0.1
        };

        this.imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.loadImage(entry.target);
                    this.imageObserver.unobserve(entry.target);
                }
            });
        }, options);

        // Observe all lazy images
        this.observeImages();
    }

    observeImages() {
        const images = document.querySelectorAll('.image-container img[loading="lazy"]');
        images.forEach(img => {
            this.imageObserver.observe(img.closest('.image-container'));
        });
    }

    loadImage(container) {
        const img = container.querySelector('img');
        const skeleton = container.querySelector('.skeleton');
        
        if (!img) return;

        // Handle image load success
        img.addEventListener('load', () => {
            img.classList.add('loaded');
            if (skeleton) {
                skeleton.classList.add('hidden');
            }
        });

        // Handle image load error
        img.addEventListener('error', () => {
            // Error handling is already in the HTML onerror attribute
            if (skeleton) {
                skeleton.classList.add('hidden');
            }
        });

        // Trigger loading if not already loaded
        if (!img.complete) {
            img.src = img.src;
        } else {
            img.classList.add('loaded');
            if (skeleton) {
                skeleton.classList.add('hidden');
            }
        }
    }

    loadAllImages() {
        // Fallback: load all images immediately
        const containers = document.querySelectorAll('.image-container');
        containers.forEach(container => {
            this.loadImage(container);
        });
    }

    // Method to manually load images (useful for dynamic content)
    loadNewImages() {
        if (this.imageObserver) {
            this.observeImages();
        } else {
            this.loadAllImages();
        }
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.optimizedImageLoader = new OptimizedImageLoader();
});

// For jQuery compatibility (if needed)
if (typeof $ !== 'undefined') {
    $(document).ready(() => {
        if (!window.optimizedImageLoader) {
            window.optimizedImageLoader = new OptimizedImageLoader();
        }
    });
}

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = OptimizedImageLoader;
}
