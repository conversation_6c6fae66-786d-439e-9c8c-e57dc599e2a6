window.userHasInteracted = false;
let isInitialized = false;
let callbacks = [];

function handleUserActivity() {
    if (window.userHasInteracted) {
        return; 
    }
    
    window.userHasInteracted = true;
    
    callbacks.forEach(callback => {
        if (callback && typeof callback === 'function') {
            callback();
        }
    });
    
    callbacks = [];
    
    window.removeEventListener('scroll', handleUserActivity);
    window.removeEventListener('mousemove', handleUserActivity);
    window.removeEventListener('touchstart', handleUserActivity);
    window.removeEventListener('touchmove', handleUserActivity);
}

function userActivityWrapper(callback = null) {
    if (isInitialized) {
        if (callback && typeof callback === 'function') {
            callbacks.push(callback);
        }
        return;
    }
    
    if (callback && typeof callback === 'function') {
        callbacks.push(callback);
    }
    
    const activityHandler = () => handleUserActivity();
    
    window.addEventListener('scroll', activityHandler, { passive: true });
    document.addEventListener('mousemove', activityHandler, { passive: true });
    document.addEventListener('touchstart', activityHandler, { passive: true });
    document.addEventListener('touchmove', activityHandler, { passive: true });
    
    isInitialized = true;
}

window.userActivityWrapper = userActivityWrapper;

