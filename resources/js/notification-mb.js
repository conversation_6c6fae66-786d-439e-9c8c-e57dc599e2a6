const VITE_TELEGRAM_LINK = import.meta.env.VITE_TELEGRAM_LINK;

document.addEventListener("DOMContentLoaded", () => {
    const cookies = getCookies();
    const user = cookies?.user ? JSON.parse(cookies.user) : null;
    if ((window.innerWidth >= 1300 || !user) && window.location.pathname === '/notification') {
        window.location.href = '/';
    }

    window.addEventListener("resize", () => {
        if (window.innerWidth >= 1300 && window.location.pathname === '/notification') {
            window.location.href = '/';
        }
    });

    $(document).on("click", "#item-notification-mb", function () {
        const item = JSON.parse($(this).attr("data-value"));
        markNotificationAsRead(item.id, item?.type)
            .then(() => {
                if (item?.link) {
                    window.location.href = item.link;
                }
            })
            .catch((error) => {
                console.error("Error marking notification as read:", error);
            });
    });
});

const renderNotificationMB = (notification = []) => {
    const totalNotification = notification?.filter(e => !e.is_readed) || [];

        if (totalNotification.length) {
        $(".total-notification").removeClass("hidden").addClass("flex").text(totalNotification.length > 99 ? "99+" : totalNotification.length);
    } else {
        $(".total-notification").removeClass("flex").addClass("hidden");
    }

    $("#notification-mobile").empty();
    
    const createElement = (item) => {
        const formatDateTime = (value, format) => {
            if (!value) return "";

            const date = typeof value === "string" ? new Date(value) : value;

            if (isNaN(date.getTime())) return "";

            const formatMap = {
                YYYY: date.getFullYear(),
                MM: String(date.getMonth() + 1).padStart(2, "0"),
                DD: String(date.getDate()).padStart(2, "0"),
                HH: String(date.getHours()).padStart(2, "0"),
                mm: String(date.getMinutes()).padStart(2, "0"),
                ss: String(date.getSeconds()).padStart(2, "0"),
            };

            return format.replace(
                /YYYY|MM|DD|HH|mm|ss/g,
                (match) => formatMap[match]
            );
        };
        const convertIcon = () => {
            switch (Number(item?.type||'0')) {
                case 1:
                    return "icon-deposit.svg";
                case 2:
                    return "icon-withdraw.svg";
                case 3:
                    return "icon-failed.svg";
                case 4:
                    return "icon-maintenance.svg";
                case 5:
                    return "icon-event.svg";
                default:
                    return "icon-event.svg";
            }
        };

        item.content = item?.content.replace('livechat','<span onclick="openLiveChat()">livechat</span>')
        .replace('telegram',`<a href="${VITE_TELEGRAM_LINK}" target="_blank">telegram</a>`);

        const isBoWithdraw = (Number(item?.type) === 3);

        if(isBoWithdraw) {
            item.content  = 'Vui lòng liên hệ chăm sóc khách hàng.';
            item.link = "";
        }

        return `
            <div 
                id="item-notification-mb" 
                class="notification-item flex items-center gap-x-2 p-2 cursor-pointer rounded-lg ${item?.is_readed ? 'notification-item-readed' : 'bg-neutral'}" 
                data-value='${JSON.stringify(item)}'>

                    <div class="item-read-notification min-w-2 min-h-2 w-2 h-2 rounded-[50%] ${!item?.is_readed ? "bg-primary-500" : "bg-transparent"}"></div>
                    <div class="icon min-w-8 min-h-8">
                        <img src="${window.location.origin}/asset/images/notification/${convertIcon()}" class="w-8 h-8" alt="icon" />
                    </div>
                    <div class="flex flex-col overflow-hidden">
                        <div class="uppercase-first-letter text-sm font-medium leading-[calc(20/14)] text-neutral-1000 truncate">${ item?.title || ""}</div>
                        <div ${isBoWithdraw ? 'onclick="openLiveChat()"' : ''} class="uppercase-first-letter text-xs leading-[calc(18/12)] text-neutral-800 truncate">${item?.content || ""}</div>
                        <div class="text-[10px] leading-[calc(14/10)] text-neutral-800">${formatDateTime(item?.created_date || "", "DD/MM/YYYY HH:mm")}</div>
                    </div>
                </div>
        `;
    };

    const emptyElement = () => {
        return `
            <div class="xl:h-[18.34375rem] h-[34.9375rem] flex items-center justify-center flex-col xl:mb-[19px]">
                <img src="${window.location.origin}/asset/images/notification/empty.svg" class="w-[68px] h-[68px] xl:w-[100px] xl:h-[100px]" alt="empty" />
                <p class="mt-4 text-neutral-800 text-[12px] leading-[calc(18/12)] xl:text-sm xl:leading-[calc(20/14)]">Không có thông báo nào.</p>
            </div>
        `;
    };

    $("#notification-mobile").append(`<div class="pb-[89px]">
            <div class="flex items-center justify-between">
                ${totalNotification?.length ? `<div id="read-all-mb" class="flex items-center gap-x-1 cursor-pointer" onclick="markNotificationAsRead('all')">
                    <img src="${window.location.origin}/asset/images/notification/tick.svg" class="w-6 h-6 " alt="icon tick" />
                    <span class="text-xs leading-[calc(18/12)] text-neutral-1000">
                        Đánh dấu đã đọc tất cả
                    </span>
                </div>` : ""}
            </div>
            ${notification?.length ? `
            <div class="mt-3 flex flex-col overflow-x-auto no-scrollbar gap-y-[2px]">
                ${notification.map((e) => createElement(e)).join("")}
            </div> ` : emptyElement()
            }
        </div>    
    `);
}

window.renderNotificationMB = renderNotificationMB;
