function openModal(modal, isSticky = true, modalId = "", hidden = false, reload = "", onClose = () => {}) {
    $("#generic-modal").html(modal);
    $('#chat-widget-container').removeClass('has-modal')

    // Store isSticky parameter for ESC key handling
    $("#generic-modal").data('isSticky', isSticky);

    if (reload) {
        $("#generic-modal").data('reload', reload);
    }
    const listModal = ['bank-list-modal-container'];
    listModal.forEach((e) => {
        if (!!$(e)) {
            $('body').addClass('no-scroll');
            $('body').addClass('block-scroll');
        } else {
            $('body').removeClass('overflow-hidden');
            $('body').removeClass('no-scroll');
            $('body').removeClass('block-scroll');
        }
    })
    if (hidden) {
        $("#generic-modal").addClass("hidden");
        $("#generic-modal").attr("type", "hidden");
    }

    if (!isSticky) {
        $(`#${modalId}`).on("click", async function (e) {
            const container = document.getElementById(`${modalId}-container`);

            if (container && !container.contains(e.target)) {
                if (onClose) {
                    onClose();
                }

                closeModal();
            }
        });
    }
    if (onClose) {
        $('.js-close-modal').on('click', (event) => {
            event.preventDefault();
            closeModal();
            onClose();
        });
    } else {
        closeModal()
    }
}

function toggleHiddenModal() {
    $("#generic-modal").toggleClass("hidden");
}

function checkSignupModal() {
    if ($('#signup-modal').is(':visible')) {
        $('body').addClass('no-scroll');
        $('body').addClass('block-scroll');
    }
}

/**
 * checkOnActiveModal
 * 
 * Check if any modal is open (has child elements).
 * If so, prevent page scrolling.
 */
const checkOnActiveModal  = ()=>{
    const notiModal = $('#noti-modal')?.children();
    const authModal = $('#auth-modal')?.children();
    const genericModal = $('#generic-modal')?.children();
    if(notiModal?.length || authModal?.length || genericModal?.length){
        $('body').addClass('overflow-hidden');
        $('body').addClass('no-scroll');
        $('body').addClass('block-scroll');
    }
}

function closeModal() {
    const reload = $(`#generic-modal`).data("reload");
    $('body').removeClass('overflow-hidden');
    $('body').removeClass('no-scroll');
    $('body').removeClass('block-scroll');
    checkSignupModal();
    
    $(`#generic-modal`).html("");
    checkOnActiveModal();
    if (reload) {
        window.location.href = reload;
    }
}

function openAuthModal(modal, isSticky = true, modalId = "") {
    $("#auth-modal").empty().html(modal);
    $('#chat-widget-container').removeClass('has-modal')
    $("body").addClass("no-scroll");
    $('body').removeClass('overflow-hidden');
    $('body').addClass('block-scroll');
    if (!isSticky) {
        $(`#${modalId}`).on("click", async function (e) {
            if ($(e.target).closest($(`#${modalId}-container`)).length === 0) {
                closeAuthModal();
            }
        });
    }
}

function closeAuthModal() {
    $(`#auth-modal`).html("");
    $("body").removeClass("no-scroll");
    $('body').removeClass('block-scroll');
    $('body').removeClass('overflow-hidden');

    if ($('#generic-modal').children().length > 0) {
        $('.js-event-modal').removeClass('!z-0 !hidden');
        $("body").addClass("no-scroll");
        $('body').addClass('block-scroll');
    }
}

function openNotificationModal(modal, isSticky = true, modalId = "", hidden = false, reload = "", onClose = () => {}) {
    $("#noti-modal").html(modal);

    // Store isSticky parameter for ESC key handling
    $("#noti-modal").data('isSticky', isSticky);

    if (reload) {
        $("#noti-modal").data('reload', reload);
    }
    const listModal = ['bank-list-modal-container'];
    listModal.forEach((e) => {
        if (!!$(e)) {
            $('body').addClass('no-scroll');
            $('body').addClass('block-scroll');
        } else {
            $('body').removeClass('overflow-hidden');
            $('body').removeClass('no-scroll');
            $('body').removeClass('block-scroll');
        }
    })
    if (hidden) {
        $("#noti-modal").addClass("hidden");
        $("#noti-modal").attr("type", "hidden");
    }

    if (!isSticky) {
        $(`#${modalId}`).on("click", async function (e) {
            const container = document.getElementById(`${modalId}-container`);

            if (container && !container.contains(e.target)) {
                if (onClose) {
                    onClose();
                }

                closeNotificationModal();
            }
        });
    }
    if (onClose) {
        $('.js-close-modal').on('click', (event) => {
            event.preventDefault();
            closeNotificationModal();
            onClose();
        });
    } else {
        closeNotificationModal()
    }
}

function closeNotificationModal() {
    const reload = $(`#noti-modal`).data("reload");
    $('body').removeClass('overflow-hidden');
    $('body').removeClass('no-scroll');
    $('body').removeClass('block-scroll');
    $(`#noti-modal`).html("");
    checkOnActiveModal();
    if (reload) {
        window.location.href = reload;
    }
}

window.openModal = openModal;
window.closeModal = closeModal;
window.openAuthModal = openAuthModal;
window.closeAuthModal = closeAuthModal;
window.toggleHiddenModal = toggleHiddenModal;
window.openNotificationModal = openNotificationModal;
window.closeNotificationModal = closeNotificationModal;

document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        if ($("#generic-modal").html().trim() !== "") {
            if ($("#noti-modal").html().trim() === "") {
                // Check if generic modal is sticky before closing
                const isGenericModalSticky = $("#generic-modal").data('isSticky');
                if (!isGenericModalSticky) {
                    closeModal();
                }
            }
        }
        if ($("#auth-modal").html().trim() !== "") {
            if ($("#noti-modal").html().trim() === "") {
                // Check if auth modal is sticky before closing
                const isAuthModalSticky = $("#auth-modal").data('isSticky');
                if (!isAuthModalSticky) {
                    closeAuthModal();
                }
            }
        }
        if ($("#noti-modal").html().trim() !== "") {
            // Check if notification modal is sticky before closing
            const isNotiModalSticky = $("#noti-modal").data('isSticky');
            if (!isNotiModalSticky) {
                closeNotificationModal();
            }
        }
    }
});
