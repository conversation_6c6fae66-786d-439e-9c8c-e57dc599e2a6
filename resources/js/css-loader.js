// CSS Loader - Asynchronously load non-critical CSS
class CSSLoader {
    constructor() {
        this.loadedStyles = new Set();
        this.init();
    }

    init() {
        // Load non-critical CSS after page load
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.loadNonCriticalCSS();
            });
        } else {
            this.loadNonCriticalCSS();
        }
    }

    loadNonCriticalCSS() {
        // Use requestIdleCallback for better performance
        if ('requestIdleCallback' in window) {
            requestIdleCallback(() => {
                this.loadStylesheet('/build/assets/css/non-critical.css');
            });
        } else {
            // Fallback for browsers without requestIdleCallback
            setTimeout(() => {
                this.loadStylesheet('/build/assets/css/non-critical.css');
            }, 100);
        }
    }

    loadStylesheet(href, media = 'all') {
        // Prevent loading the same stylesheet multiple times
        if (this.loadedStyles.has(href)) {
            return Promise.resolve();
        }

        return new Promise((resolve, reject) => {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = href;
            link.media = media;
            
            // Handle load success
            link.onload = () => {
                this.loadedStyles.add(href);
                resolve();
            };
            
            // Handle load error
            link.onerror = () => {
                reject(new Error(`Failed to load stylesheet: ${href}`));
            };
            
            // Add to document head
            document.head.appendChild(link);
        });
    }

    // Load page-specific CSS on demand
    loadPageCSS(page) {
        const pageStylesheets = {
            'home': '/build/assets/css/pages/home.css',
            'sports': '/build/assets/css/pages/sports.css',
            'account': '/build/assets/css/pages/account.css',
            'news': '/build/assets/css/pages/news.css',
            'games': '/build/assets/css/pages/games.css'
        };

        if (pageStylesheets[page]) {
            return this.loadStylesheet(pageStylesheets[page]);
        }
        
        return Promise.resolve();
    }

    // Load component-specific CSS on demand
    loadComponentCSS(component) {
        const componentStylesheets = {
            'modal': '/build/assets/css/components/modal.css',
            'dropdown': '/build/assets/css/components/dropdown.css',
            'swiper': '/build/assets/css/components/swiper.css',
            'toast': '/build/assets/css/components/toast.css'
        };

        if (componentStylesheets[component]) {
            return this.loadStylesheet(componentStylesheets[component]);
        }
        
        return Promise.resolve();
    }

    // Preload CSS for better performance
    preloadStylesheet(href) {
        if (this.loadedStyles.has(href)) {
            return;
        }

        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'style';
        link.href = href;
        link.onload = () => {
            // Convert preload to stylesheet
            link.rel = 'stylesheet';
            this.loadedStyles.add(href);
        };
        
        document.head.appendChild(link);
    }

    // Remove unused CSS (for SPA navigation)
    removeStylesheet(href) {
        const links = document.querySelectorAll(`link[href="${href}"]`);
        links.forEach(link => {
            if (link.rel === 'stylesheet') {
                link.remove();
                this.loadedStyles.delete(href);
            }
        });
    }

    // Get critical CSS inline for faster rendering
    static inlineCriticalCSS() {
        // This would be called during build process to inline critical CSS
        const criticalCSS = `
            /* Critical CSS will be inlined here during build */
            body { margin: 0; padding: 0; }
            .container { max-width: 1200px; margin: 0 auto; }
            /* Add other critical styles */
        `;
        
        const style = document.createElement('style');
        style.textContent = criticalCSS;
        document.head.insertBefore(style, document.head.firstChild);
    }
}

// Auto-initialize CSS loader
const cssLoader = new CSSLoader();

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CSSLoader;
}

// Make available globally
window.cssLoader = cssLoader;

// Load page-specific CSS based on body class or data attribute
document.addEventListener('DOMContentLoaded', () => {
    const body = document.body;
    
    // Check for page identifier
    const pageClass = body.className.match(/page-(\w+)/);
    if (pageClass) {
        cssLoader.loadPageCSS(pageClass[1]);
    }
    
    // Check for data-page attribute
    const dataPage = body.getAttribute('data-page');
    if (dataPage) {
        cssLoader.loadPageCSS(dataPage);
    }
});

// Intersection Observer to load CSS when components come into view
const componentObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            const component = entry.target.getAttribute('data-component');
            if (component) {
                cssLoader.loadComponentCSS(component);
                componentObserver.unobserve(entry.target);
            }
        }
    });
}, {
    rootMargin: '50px'
});

// Observe elements with data-component attribute
document.addEventListener('DOMContentLoaded', () => {
    const componentElements = document.querySelectorAll('[data-component]');
    componentElements.forEach(el => {
        componentObserver.observe(el);
    });
});

// Preload CSS on hover for better UX
document.addEventListener('mouseover', (e) => {
    const link = e.target.closest('a[data-preload-css]');
    if (link) {
        const cssPath = link.getAttribute('data-preload-css');
        cssLoader.preloadStylesheet(cssPath);
    }
});

// Handle route changes for SPA-like behavior
window.addEventListener('popstate', () => {
    // Reload page-specific CSS for new route
    const newPage = window.location.pathname.split('/')[1] || 'home';
    cssLoader.loadPageCSS(newPage);
});
