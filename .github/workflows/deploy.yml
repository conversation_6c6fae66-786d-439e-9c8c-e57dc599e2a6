name: Deploy
on:
  push:
    branches: [dev-deploy]

jobs:
  deploy:
    name: Deploy
    runs-on: ubuntu-latest
    steps:
      - name: Request build
        run: |
          curl --location 'https://api.telegram.org/bot8155548375:AAHTq1ACeMH_CTlojwkSKhLa4qgYKMLvlUc/sendMessage' \
          --header 'Content-Type: application/x-www-form-urlencoded' \
          --data-urlencode 'chat_id=-1002513182802' \
          --data-urlencode 'text=z44big'
