#!/bin/bash

# Performance Optimization Script
echo "🚀 Starting Performance Optimization..."

# 1. Install dependencies if needed
echo "📦 Checking dependencies..."
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is required but not installed."
    exit 1
fi

if ! command -v npm &> /dev/null; then
    echo "❌ npm is required but not installed."
    exit 1
fi

# 2. Install Sharp if not already installed
echo "🖼️  Checking Sharp installation..."
if ! npm list sharp &> /dev/null; then
    echo "Installing Sharp..."
    npm install sharp
fi

# 3. Convert existing images to modern formats
echo "🔄 Converting images to modern formats..."
npm run convert:image

# 4. Regenerate autoloader to include new helper functions
echo "🔧 Regenerating autoloader..."
composer dump-autoload

# 5. Clear Laravel caches
echo "🧹 Clearing caches..."
php artisan cache:clear
php artisan config:clear
php artisan view:clear

# 6. Build assets with Vite
echo "🏗️  Building optimized assets..."
npm run build

# 7. Optimize images using the new command
echo "🖼️  Running image optimization..."
php artisan images:optimize --quality=60 --formats=avif,webp

echo "✅ Performance optimization completed!"
echo ""
echo "📊 Next steps:"
echo "1. Test your website performance with Google PageSpeed Insights"
echo "2. Check that images are loading in AVIF/WebP formats"
echo "3. Verify cache headers are working correctly"
echo "4. Monitor Core Web Vitals improvements"
echo ""
echo "🔧 Additional optimizations you can do:"
echo "1. Enable Redis/Memcached for better caching"
echo "2. Use a CDN for static assets"
echo "3. Enable HTTP/2 on your server"
echo "4. Consider using a service worker for caching"
